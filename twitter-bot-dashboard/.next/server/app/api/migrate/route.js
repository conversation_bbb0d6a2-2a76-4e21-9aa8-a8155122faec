(()=>{var e={};e.id=721,e.ids=[721],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8607:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(29021),i=r.n(s),o=r(33873),n=r.n(o),a=r(56621);async function c(){try{console.log("Starting migration of existing Twitter bot data...");let e=n().join(process.cwd(),"..","posted.json");if(i().existsSync(e)){console.log("Found existing posted.json file");let t=JSON.parse(i().readFileSync(e,"utf8"));if(Array.isArray(t)){for(let e of(console.log(`Found ${t.length} posted URLs`),t))try{await a.J0.addPostedTweet({content:`Migrated from existing bot: ${e}`,original_url:e,original_title:"Migrated Content",impressions:0,retweets:0,likes:0,replies:0,posted_at:new Date().toISOString()})}catch(t){t.message?.includes("duplicate")||console.error(`Error migrating URL ${e}:`,t)}console.log("Migration of posted URLs completed")}}else console.log("No existing posted.json file found");for(let e of(console.log("Ensuring default RSS feeds are configured..."),[{name:"TechCrunch",url:"https://techcrunch.com/feed/"},{name:"The Verge",url:"https://www.theverge.com/rss/index.xml"},{name:"Crunchbase News",url:"https://news.crunchbase.com/feed/"},{name:"Android Police",url:"https://www.androidpolice.com/feed/"}]))try{await a.J0.addRSSFeed(e.name,e.url),console.log(`Added RSS feed: ${e.name}`)}catch(t){t.message?.includes("duplicate")||console.error(`Error adding RSS feed ${e.name}:`,t)}console.log("Migration completed successfully!")}catch(e){throw console.error("Error during migration:",e),e}}e=r.hmd(e);let d=c;r.c[r.s]===e&&c().then(()=>{console.log("Migration script completed"),process.exit(0)}).catch(e=>{console.error("Migration script failed:",e),process.exit(1)})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},54354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var s={};r.r(s),r.d(s,{POST:()=>d});var i=r(96559),o=r(48088),n=r(37719),a=r(32190),c=r(8607);async function d(){try{return await (0,c.A)(),a.NextResponse.json({message:"Migration completed successfully",success:!0})}catch(e){return console.error("Migration failed:",e),a.NextResponse.json({error:"Migration failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/migrate/route",pathname:"/api/migrate",filename:"route",bundlePath:"app/api/migrate/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:g}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>a});var s=r(66437);let i="https://fmhujzbqfzyyffgzwtzb.supabase.co",o=process.env.SUPABASE_SERVICE_ROLE_KEY,n=(0,s.UU)(i,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(i,o);let a={async getRSSFeeds(){let{data:e,error:t}=await n.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await n.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await n.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await n.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await n.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await n.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await n.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await n.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await n.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await n.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await n.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await n.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await n.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await n.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await n.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437],()=>r(54354));module.exports=s})();