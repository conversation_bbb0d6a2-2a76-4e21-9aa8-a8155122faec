(()=>{var e={};e.id=335,e.ids=[335],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},48592:(e,t,r)=>{"use strict";r.d(t,{e$:()=>i});var s=r(73395);let a=new s.Yl({clientId:process.env.TWITTER_CLIENT_ID,clientSecret:process.env.TWITTER_CLIENT_SECRET,accessToken:process.env.TWITTER_ACCESS_TOKEN,refreshToken:process.env.TWITTER_REFRESH_TOKEN});new s.Yl({appKey:process.env.TWITTER_APP_KEY,appSecret:process.env.TWITTER_APP_SECRET,accessToken:process.env.TWITTER_ACCESS_TOKEN,accessSecret:process.env.TWITTER_ACCESS_SECRET});let i={async getCurrentUser(){try{let{client:e}=await a.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN),t=await e.v2.me({"user.fields":["public_metrics","verified","profile_image_url"]});return{id:t.data.id,username:t.data.username,name:t.data.name,followers_count:t.data.public_metrics?.followers_count||0,following_count:t.data.public_metrics?.following_count||0,tweet_count:t.data.public_metrics?.tweet_count||0,verified:t.data.verified||!1,profile_image_url:t.data.profile_image_url}}catch(e){throw console.error("Error fetching current user:",e),e}},async postTweet(e){try{let{client:t,accessToken:r,refreshToken:s}=await a.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN);return process.env.TWITTER_ACCESS_TOKEN=r,process.env.TWITTER_REFRESH_TOKEN=s,{id:(await t.v2.tweet(e)).data.id,text:e,created_at:new Date().toISOString()}}catch(e){throw console.error("Error posting tweet:",e),e}},async getRecentTweets(e=10){try{let t=await this.getCurrentUser(),r=await a.v2.userTimeline(t.id,{max_results:e,"tweet.fields":["created_at","public_metrics"]});return r.data?.map(e=>({id:e.id,text:e.text,created_at:e.created_at||new Date().toISOString(),public_metrics:e.public_metrics}))||[]}catch(e){throw console.error("Error fetching recent tweets:",e),e}},async getTweetAnalytics(e){try{let t=await a.v2.singleTweet(e,{"tweet.fields":["created_at","public_metrics"]});if(!t.data)return null;return{id:t.data.id,text:t.data.text,created_at:t.data.created_at||new Date().toISOString(),public_metrics:t.data.public_metrics}}catch(e){return console.error("Error fetching tweet analytics:",e),null}},async getTotalImpressions(e=30){try{await this.getCurrentUser();let t=await this.getRecentTweets(100),r=new Date;r.setDate(r.getDate()-e);let s=t.filter(e=>new Date(e.created_at)>=r),a=0;for(let e of s)if(e.public_metrics?.impression_count)a+=e.public_metrics.impression_count;else if(e.public_metrics){let t=(e.public_metrics.like_count||0)+(e.public_metrics.retweet_count||0)+(e.public_metrics.reply_count||0);a+=Math.max(10*t,100)}return a}catch(e){return console.error("Error calculating total impressions:",e),0}},async refreshToken(){try{let{accessToken:e,refreshToken:t}=await a.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN);return{accessToken:e,refreshToken:t}}catch(e){throw console.error("Error refreshing token:",e),e}}}},55422:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),c=r(48592),l=r(56621);async function u(e){try{let e=await c.e$.getCurrentUser(),t=await c.e$.getRecentTweets(50),r=0;for(let e of t)e.public_metrics&&(r+=(e.public_metrics.like_count||0)+(e.public_metrics.retweet_count||0)+(e.public_metrics.reply_count||0)+(e.public_metrics.quote_count||0));let s=await c.e$.getTotalImpressions(30),a=await l.J0.getPostedTweets(1e3);a.length;let i=a.reduce((e,t)=>e+t.impressions,0);await l.J0.addAnalytics({followers_count:e.followers_count,following_count:e.following_count,total_tweets:e.tweet_count,total_impressions:Math.max(s,i),total_engagements:r});let n={followers:e.followers_count,totalTweets:e.tweet_count,totalImpressions:Math.max(s,i),totalEngagements:r,recentTweets:t.length,username:e.username,name:e.name,verified:e.verified,profileImage:e.profile_image_url};return o.NextResponse.json(n)}catch(e){console.error("Error fetching Twitter stats:",e);try{let e=await l.J0.getLatestAnalytics(),t=await l.J0.getPostedTweets(1e3);if(e)return o.NextResponse.json({followers:e.followers_count,totalTweets:e.total_tweets,totalImpressions:e.total_impressions,totalEngagements:e.total_engagements,recentTweets:t.length,username:"N/A",name:"N/A",verified:!1,profileImage:null})}catch(e){console.error("Error fetching fallback data:",e)}return o.NextResponse.json({error:"Failed to fetch Twitter stats"},{status:500})}}async function p(e){try{let e=await c.e$.getCurrentUser(),t=await c.e$.getTotalImpressions(30);return await l.J0.addAnalytics({followers_count:e.followers_count,following_count:e.following_count,total_tweets:e.tweet_count,total_impressions:t,total_engagements:0}),o.NextResponse.json({success:!0})}catch(e){return console.error("Error refreshing Twitter stats:",e),o.NextResponse.json({error:"Failed to refresh Twitter stats"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/twitter/stats/route",pathname:"/api/twitter/stats",filename:"route",bundlePath:"app/api/twitter/stats/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:_,serverHooks:f}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:_})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>o});var s=r(66437);let a="https://fmhujzbqfzyyffgzwtzb.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY,n=(0,s.UU)(a,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(a,i);let o={async getRSSFeeds(){let{data:e,error:t}=await n.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await n.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await n.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await n.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await n.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await n.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await n.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await n.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await n.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await n.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await n.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await n.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await n.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await n.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await n.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437,395],()=>r(55422));module.exports=s})();