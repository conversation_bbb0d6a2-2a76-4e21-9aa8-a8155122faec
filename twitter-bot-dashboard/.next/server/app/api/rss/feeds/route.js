(()=>{var e={};e.id=836,e.ids=[836],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4721:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>c});var n=r(96559),i=r(48088),a=r(37719),o=r(32190),u=r(56621);async function d(){try{let e=await u.J0.getRSSFeeds();return o.NextResponse.json(e)}catch(e){return console.error("Error fetching RSS feeds:",e),o.NextResponse.json({error:"Failed to fetch RSS feeds"},{status:500})}}async function c(e){try{let{name:t,url:r}=await e.json();if(!t||!r)return o.NextResponse.json({error:"Name and URL are required"},{status:400});let s=await u.J0.addRSSFeed(t,r);return o.NextResponse.json(s,{status:201})}catch(e){return console.error("Error adding RSS feed:",e),o.NextResponse.json({error:"Failed to add RSS feed"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/rss/feeds/route",pathname:"/api/rss/feeds",filename:"route",bundlePath:"app/api/rss/feeds/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:w}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>o});var s=r(66437);let n="https://fmhujzbqfzyyffgzwtzb.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.UU)(n,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(n,i);let o={async getRSSFeeds(){let{data:e,error:t}=await a.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await a.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await a.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await a.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await a.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await a.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await a.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await a.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await a.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await a.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await a.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await a.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await a.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437],()=>r(4721));module.exports=s})();