(()=>{var e={};e.id=993,e.ids=[993],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33455:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(5648),n=r.n(s),o=r(87537);let a=new(n()),i={async parseFeed(e){try{return(await a.parseURL(e)).items.map(e=>({title:e.title||"",link:e.link||"",pubDate:e.pubDate,contentSnippet:e.contentSnippet,content:e.content,guid:e.guid||e.link||"",categories:e.categories||[],author:e.author}))}catch(t){throw console.error(`Error parsing RSS feed ${e}:`,t),t}},async extractContent(e){try{let t=await (0,o.o6)(e);return t?.content||null}catch(t){return console.error(`Error extracting content from ${e}:`,t),null}},async processRSSItems(e){let t=[];for(let r of e)try{let e=await this.extractContent(r.link);e&&t.push({title:r.title,url:r.link,content:e,publishedAt:r.pubDate||new Date().toISOString(),categories:r.categories||[],author:r.author,guid:r.guid})}catch(e){console.error(`Error processing item ${r.title}:`,e)}return t},deduplicateContent(e){let t=new Set,r=[];for(let s of e){let e=s.title.toLowerCase().replace(/[^\w\s]/g,"").trim(),n=`${s.url}|${e}`;t.has(n)||(t.add(n),r.push(s))}return r},async getAggregatedContent(e){let t=[];for(let r of e)try{let e=await this.parseFeed(r),s=await this.processRSSItems(e);t.push(...s)}catch(e){console.error(`Error processing feed ${r}:`,e)}return this.deduplicateContent(t).sort((e,t)=>new Date(t.publishedAt).getTime()-new Date(e.publishedAt).getTime())},filterContent:(e,t=[])=>0===t.length?e:e.filter(e=>{let r=`${e.title} ${e.content} ${e.categories.join(" ")}`.toLowerCase();return t.some(e=>r.includes(e.toLowerCase()))}),scoreContent:e=>e.map(e=>{let t;t=0+Math.max(0,100-(Date.now()-new Date(e.publishedAt).getTime())/36e5);let r=e.title.split(" ").length;r>=5&&r<=15&&(t+=20);let s=e.content.split(" ").length;s>=200&&s<=2e3&&(t+=30);let n=["tech","technology","ai","startup","innovation","software","app"];return e.categories.some(e=>n.some(t=>e.toLowerCase().includes(t)))&&(t+=25),{...e,score:t}}).sort((e,t)=>t.score-e.score)}},33509:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let s=new(r(40694)).Ay({apiKey:process.env.OPENAI_API_KEY}),n={async generateTweetContent(e,t,r,n={}){let{tone:o="analytical",includePersonalTouch:a=!0,maxLength:i=280,includeHashtags:c=!0}=n;try{let n=`
You are an expert social media content creator specializing in tech and startup content. 
Create engaging Twitter content based on this article:

Title: ${e}
Content: ${t.slice(0,2e3)}
URL: ${r}

Requirements:
1. Generate a SHORT HOOK (1-2 sentences, max 50 characters) that grabs attention
2. Generate a LONG HOOK (2-3 sentences, max 100 characters) that provides more context
3. Generate a PERSONAL TOUCH message that adds your unique perspective or insight
4. Create the final TWEET CONTENT (max ${i} characters) combining the best elements
5. Suggest relevant hashtags

Tone: ${o}
Include personal perspective: ${a}

Format your response as JSON:
{
  "shortHook": "...",
  "longHook": "...",
  "personalTouch": "...",
  "tweetContent": "...",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3"]
}

Best practices for maximum engagement:
- Start with a compelling hook
- Use numbers, statistics, or surprising facts when available
- Ask questions or create curiosity gaps
- Include actionable insights
- Use power words and emotional triggers
- Keep it conversational and authentic
- Add your unique perspective or hot take
`,c=await s.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:n}],max_tokens:500,temperature:.7}),l=c.choices[0].message.content?.trim();if(!l)throw Error("No response from OpenAI");let u=JSON.parse(l);return{shortHook:u.shortHook||"",longHook:u.longHook||"",personalTouch:u.personalTouch||"",tweetContent:u.tweetContent||"",hashtags:Array.isArray(u.hashtags)?u.hashtags:[]}}catch(s){return console.error("Error generating tweet content:",s),this.generateFallbackContent(e,t,r,n)}},async generateTweetVariations(e,t,r,s=3,n={}){let o=[];for(let a=0;a<s;a++)try{let s=await this.generateTweetContent(e,t,r,{...n,tone:["analytical","casual","enthusiastic"][a%3]});o.push(s)}catch(e){console.error(`Error generating variation ${a+1}:`,e)}return o},async generateContentSummary(e,t){try{let r=`
Summarize this article in 2-3 sentences, focusing on the key insights and takeaways:

Title: ${e}
Content: ${t.slice(0,1500)}

Make it engaging and highlight what makes this newsworthy or interesting.
`,n=await s.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:r}],max_tokens:150,temperature:.5});return n.choices[0].message.content?.trim()||"Summary not available"}catch(e){return console.error("Error generating summary:",e),"Summary not available"}},async analyzeEngagementPotential(e,t){try{let r=`
Analyze this content for Twitter engagement potential:

Title: ${e}
Content: ${t.slice(0,1e3)}

Rate the engagement potential (1-10) and provide:
1. Key factors that make it engaging or not
2. Suggestions to improve engagement

Format as JSON:
{
  "score": 7,
  "factors": ["factor1", "factor2"],
  "suggestions": ["suggestion1", "suggestion2"]
}
`,n=await s.chat.completions.create({model:"gpt-4o",messages:[{role:"user",content:r}],max_tokens:300,temperature:.3}),o=n.choices[0].message.content?.trim();if(!o)throw Error("No response from OpenAI");return JSON.parse(o)}catch(e){return console.error("Error analyzing engagement potential:",e),{score:5,factors:["Unable to analyze"],suggestions:["Try again later"]}}},generateFallbackContent(e,t,r,s={}){let n=e.slice(0,50)+(e.length>50?"...":""),o=`Interesting read: ${e.slice(0,80)}${e.length>80?"...":""}`,a="Worth checking out this perspective on the latest tech developments.",i=`${n}

${a}

${r}`;return{shortHook:n,longHook:o,personalTouch:a,tweetContent:i.length>(s.maxLength||280)?`${e.slice(0,200)}...

${r}`:i,hashtags:["tech","news","startup"]}}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46486:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>f,serverHooks:()=>S,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{GET:()=>m,POST:()=>_});var n=r(96559),o=r(48088),a=r(37719),i=r(32190);let c=require("node-cron");var l=r.n(c),u=r(56621),p=r(48592),d=r(33455),g=r(33509);class h{async start(){if(this.isRunning)return void console.log("Scheduler is already running");try{let e=await u.J0.getUserPreferences(),t=e?.posting_interval_minutes||30,r=`*/${t} * * * *`;console.log(`Starting Twitter bot scheduler with ${t} minute intervals`),this.cronJob=l().schedule(r,async()=>{await this.processAutomatedPosting()},{scheduled:!1}),this.cronJob.start(),this.isRunning=!0,console.log("Twitter bot scheduler started successfully")}catch(e){console.error("Error starting scheduler:",e)}}stop(){this.cronJob&&(this.cronJob.stop(),this.cronJob=null),this.isRunning=!1,console.log("Twitter bot scheduler stopped")}async processAutomatedPosting(){try{console.log("Processing automated posting...");let e=await u.J0.getUserPreferences();if(!e?.auto_post_enabled)return void console.log("Auto-posting is disabled, skipping...");await this.refreshContentQueue();let t=(await u.J0.getContentQueue(50)).filter(e=>!e.is_posted&&e.ai_generated_content&&e.priority_score>50);if(0===t.length)return void console.log("No suitable content found for automated posting");let r=t.sort((e,t)=>t.priority_score-e.priority_score)[0],s=await p.e$.postTweet(r.ai_generated_content);await u.J0.addPostedTweet({tweet_id:s.id,content:r.ai_generated_content,original_url:r.original_url,original_title:r.original_title,impressions:0,retweets:0,likes:0,replies:0,posted_at:s.created_at}),await u.J0.markContentAsPosted([r.id]),console.log(`Successfully posted automated tweet: ${r.original_title}`)}catch(e){console.error("Error in automated posting:",e)}}async refreshContentQueue(){try{let e=(await u.J0.getRSSFeeds()).filter(e=>e.is_active);if(0===e.length)return void console.log("No active RSS feeds found");let t=e.map(e=>e.url),r=await d.A.getAggregatedContent(t),s=d.A.scoreContent(r),n=await u.J0.getContentQueue(1e3),o=new Set(n.map(e=>e.original_url)),a=0;for(let t of s.slice(0,10))if(!o.has(t.url))try{let r=e.find(e=>t.url.includes(new URL(e.url).hostname))?.id,s=await u.J0.addToContentQueue({original_url:t.url,original_title:t.title,original_content:t.content,rss_feed_id:r,is_selected:!1,is_posted:!1,priority_score:t.score});if(t.score>70)try{let e=await g.A.generateTweetContent(t.title,t.content,t.url,{tone:"analytical",includePersonalTouch:!0});await u.J0.updateContentQueue(s.id,{ai_generated_content:e.tweetContent,short_hook:e.shortHook,long_hook:e.longHook,personal_touch:e.personalTouch})}catch(e){console.error(`Error generating AI content for ${t.title}:`,e)}a++}catch(e){console.error(`Error adding content item: ${t.title}`,e)}console.log(`Added ${a} new items to content queue`)}catch(e){console.error("Error refreshing content queue:",e)}}async updateAnalytics(){try{let e=await p.e$.getCurrentUser(),t=await p.e$.getRecentTweets(50),r=0;for(let e of t)e.public_metrics&&(r+=(e.public_metrics.like_count||0)+(e.public_metrics.retweet_count||0)+(e.public_metrics.reply_count||0)+(e.public_metrics.quote_count||0));let s=await p.e$.getTotalImpressions(30);await u.J0.addAnalytics({followers_count:e.followers_count,following_count:e.following_count,total_tweets:e.tweet_count,total_impressions:s,total_engagements:r}),console.log("Analytics updated successfully")}catch(e){console.error("Error updating analytics:",e)}}getStatus(){return{isRunning:this.isRunning,cronJob:this.cronJob?"Active":"Inactive"}}constructor(){this.isRunning=!1,this.cronJob=null}}let w=new h;async function m(e){try{let e=w.getStatus();return i.NextResponse.json(e)}catch(e){return console.error("Error getting scheduler status:",e),i.NextResponse.json({error:"Failed to get scheduler status"},{status:500})}}async function _(e){try{let{action:t}=await e.json();if("start"===t)return await w.start(),i.NextResponse.json({message:"Scheduler started successfully"});if("stop"===t)return w.stop(),i.NextResponse.json({message:"Scheduler stopped successfully"});if("process"===t)return await w.processAutomatedPosting(),i.NextResponse.json({message:"Manual posting process completed"});else return i.NextResponse.json({error:'Invalid action. Use "start", "stop", or "process"'},{status:400})}catch(e){return console.error("Error controlling scheduler:",e),i.NextResponse.json({error:"Failed to control scheduler"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/scheduler/route",pathname:"/api/scheduler",filename:"route",bundlePath:"app/api/scheduler/route"},resolvedPagePath:"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:T,serverHooks:S}=f;function E(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:T})}},47990:()=>{},48592:(e,t,r)=>{"use strict";r.d(t,{e$:()=>o});var s=r(73395);let n=new s.Yl({clientId:process.env.TWITTER_CLIENT_ID,clientSecret:process.env.TWITTER_CLIENT_SECRET,accessToken:process.env.TWITTER_ACCESS_TOKEN,refreshToken:process.env.TWITTER_REFRESH_TOKEN});new s.Yl({appKey:process.env.TWITTER_APP_KEY,appSecret:process.env.TWITTER_APP_SECRET,accessToken:process.env.TWITTER_ACCESS_TOKEN,accessSecret:process.env.TWITTER_ACCESS_SECRET});let o={async getCurrentUser(){try{let{client:e}=await n.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN),t=await e.v2.me({"user.fields":["public_metrics","verified","profile_image_url"]});return{id:t.data.id,username:t.data.username,name:t.data.name,followers_count:t.data.public_metrics?.followers_count||0,following_count:t.data.public_metrics?.following_count||0,tweet_count:t.data.public_metrics?.tweet_count||0,verified:t.data.verified||!1,profile_image_url:t.data.profile_image_url}}catch(e){throw console.error("Error fetching current user:",e),e}},async postTweet(e){try{let{client:t,accessToken:r,refreshToken:s}=await n.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN);return process.env.TWITTER_ACCESS_TOKEN=r,process.env.TWITTER_REFRESH_TOKEN=s,{id:(await t.v2.tweet(e)).data.id,text:e,created_at:new Date().toISOString()}}catch(e){throw console.error("Error posting tweet:",e),e}},async getRecentTweets(e=10){try{let t=await this.getCurrentUser(),r=await n.v2.userTimeline(t.id,{max_results:e,"tweet.fields":["created_at","public_metrics"]});return r.data?.map(e=>({id:e.id,text:e.text,created_at:e.created_at||new Date().toISOString(),public_metrics:e.public_metrics}))||[]}catch(e){throw console.error("Error fetching recent tweets:",e),e}},async getTweetAnalytics(e){try{let t=await n.v2.singleTweet(e,{"tweet.fields":["created_at","public_metrics"]});if(!t.data)return null;return{id:t.data.id,text:t.data.text,created_at:t.data.created_at||new Date().toISOString(),public_metrics:t.data.public_metrics}}catch(e){return console.error("Error fetching tweet analytics:",e),null}},async getTotalImpressions(e=30){try{await this.getCurrentUser();let t=await this.getRecentTweets(100),r=new Date;r.setDate(r.getDate()-e);let s=t.filter(e=>new Date(e.created_at)>=r),n=0;for(let e of s)if(e.public_metrics?.impression_count)n+=e.public_metrics.impression_count;else if(e.public_metrics){let t=(e.public_metrics.like_count||0)+(e.public_metrics.retweet_count||0)+(e.public_metrics.reply_count||0);n+=Math.max(10*t,100)}return n}catch(e){return console.error("Error calculating total impressions:",e),0}},async refreshToken(){try{let{accessToken:e,refreshToken:t}=await n.refreshOAuth2Token(process.env.TWITTER_REFRESH_TOKEN);return{accessToken:e,refreshToken:t}}catch(e){throw console.error("Error refreshing token:",e),e}}}},48817:e=>{"use strict";e.exports=require("postcss")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{J0:()=>i});var s=r(66437);let n="https://fmhujzbqfzyyffgzwtzb.supabase.co",o=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.UU)(n,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZtaHVqemJxZnp5eWZmZ3p3dHpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExOTAxNTUsImV4cCI6MjA2Njc2NjE1NX0.__Hn0engp4WtLiH5O02HEps4GwU6YNwi9sF6lHQIg30");(0,s.UU)(n,o);let i={async getRSSFeeds(){let{data:e,error:t}=await a.from("rss_feeds").select("*").order("created_at",{ascending:!1});if(t)throw t;return e},async addRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").insert({name:e,url:t}).select().single();if(s)throw s;return r},async updateRSSFeed(e,t){let{data:r,error:s}=await a.from("rss_feeds").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getPostedTweets(e=50){let{data:t,error:r}=await a.from("posted_tweets").select("*").order("posted_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addPostedTweet(e){let{data:t,error:r}=await a.from("posted_tweets").insert(e).select().single();if(r)throw r;return t},async getLatestAnalytics(){let{data:e,error:t}=await a.from("twitter_analytics").select("*").order("recorded_at",{ascending:!1}).limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async addAnalytics(e){let{data:t,error:r}=await a.from("twitter_analytics").insert(e).select().single();if(r)throw r;return t},async getUserPreferences(){let{data:e,error:t}=await a.from("user_preferences").select("*").limit(1).single();if(t&&"PGRST116"!==t.code)throw t;return e},async updateUserPreferences(e){let{data:t,error:r}=await a.from("user_preferences").update({...e,updated_at:new Date().toISOString()}).select().single();if(r)throw r;return t},async getContentQueue(e=20){let{data:t,error:r}=await a.from("content_queue").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw r;return t},async addToContentQueue(e){let{data:t,error:r}=await a.from("content_queue").insert(e).select().single();if(r)throw r;return t},async updateContentQueue(e,t){let{data:r,error:s}=await a.from("content_queue").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(s)throw s;return r},async getSelectedContent(){let{data:e,error:t}=await a.from("content_queue").select("*").eq("is_selected",!0).eq("is_posted",!1).order("priority_score",{ascending:!1});if(t)throw t;return e},async markContentAsPosted(e){let{data:t,error:r}=await a.from("content_queue").update({is_posted:!0,is_selected:!1,updated_at:new Date().toISOString()}).in("id",e).select();if(r)throw r;return t},async deleteRSSFeed(e){let{error:t}=await a.from("rss_feeds").delete().eq("id",e);if(t)throw t;return!0}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,437,38,395,694],()=>r(46486));module.exports=s})();