(()=>{var e={};e.id=974,e.ids=[974],e.modules={100:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39248:()=>{},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81104:()=>{},81123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(43210),n=r(78122),o=r(62688);let i=(0,o.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),l=(0,o.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),d=(0,o.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),c=(0,o.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);function p(){let[e,t]=(0,a.useState)({followers:0,totalTweets:0,totalImpressions:0,totalEngagements:0,recentTweets:0}),[r,o]=(0,a.useState)(!0),[p,m]=(0,a.useState)(null),h=async()=>{try{o(!0);let e=await fetch("/api/twitter/stats");if(e.ok){let r=await e.json();t(r),m(new Date)}}catch(e){console.error("Error fetching stats:",e)}finally{o(!1)}},x=({title:e,value:t,icon:r,change:a,color:n="blue"})=>(0,s.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:"number"==typeof t?t.toLocaleString():t}),a&&(0,s.jsx)("p",{className:"text-sm text-green-600 mt-1",children:a})]}),(0,s.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-50 border-blue-200 text-blue-700",green:"bg-green-50 border-green-200 text-green-700",purple:"bg-purple-50 border-purple-200 text-purple-700",orange:"bg-orange-50 border-orange-200 text-orange-700"}[n]}`,children:(0,s.jsx)(r,{className:"h-6 w-6"})})]})});return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Twitter Bot Dashboard"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Monitor your automated Twitter bot performance"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[p&&(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Last updated: ",p.toLocaleTimeString()]}),(0,s.jsxs)("button",{type:"button",onClick:h,disabled:r,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,s.jsx)(n.A,{className:`h-4 w-4 mr-2 ${r?"animate-spin":""}`}),"Refresh"]})]})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)(x,{title:"Followers",value:e.followers,icon:i,color:"blue"}),(0,s.jsx)(x,{title:"Total Tweets",value:e.totalTweets,icon:l,color:"green"}),(0,s.jsx)(x,{title:"Total Impressions",value:e.totalImpressions,icon:d,color:"purple"}),(0,s.jsx)(x,{title:"Total Engagements",value:e.totalEngagements,icon:c,color:"orange"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("a",{href:"/feeds",className:"block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"RSS Feeds"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Manage RSS feeds and view aggregated content"})]}),(0,s.jsxs)("a",{href:"/content",className:"block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Content Queue"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Review and select content for posting"})]})]})]})]})]})}},81540:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},87465:(e,t,r)=>{Promise.resolve().then(r.bind(r,81123))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),a=r(2202),n=r.n(a),o=r(64988),i=r.n(o);r(61135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:e})})}},97193:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,169,75],()=>r(16488));module.exports=s})();