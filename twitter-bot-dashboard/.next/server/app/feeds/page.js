(()=>{var e={};e.id=688,e.ids=[688],e.modules={100:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27978:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29364:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["feeds",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,27978)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/feeds/page",pathname:"/feeds",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},39248:()=>{},46881:(e,t,s)=>{Promise.resolve().then(s.bind(s,27978))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81104:()=>{},81540:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},83329:(e,t,s)=>{Promise.resolve().then(s.bind(s,94849))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(37413),a=s(2202),n=s.n(a),i=s(64988),o=s.n(i);s(61135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:e})})}},94849:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),a=s(43210),n=s(78122),i=s(28559),o=s(62688);let l=(0,o.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),d=(0,o.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),c=(0,o.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),m=(0,o.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var x=s(85814),h=s.n(x);function p(){let[e,t]=(0,a.useState)([]),[s,o]=(0,a.useState)([]),[x,p]=(0,a.useState)(!0),[u,b]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),[y,v]=(0,a.useState)({name:"",url:""}),j=async()=>{try{let e=await fetch("/api/rss/feeds");if(e.ok){let s=await e.json();t(s)}}catch(e){console.error("Error fetching feeds:",e)}},w=async()=>{try{b(!0);let e=await fetch("/api/rss/items");if(e.ok){let t=await e.json();o(t)}}catch(e){console.error("Error fetching feed items:",e)}finally{b(!1)}},N=async e=>{e.preventDefault();try{(await fetch("/api/rss/feeds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)})).ok&&(v({name:"",url:""}),g(!1),await j())}catch(e){console.error("Error adding feed:",e)}},k=async(e,t)=>{try{(await fetch(`/api/rss/feeds/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!t})})).ok&&await j()}catch(e){console.error("Error toggling feed:",e)}},P=async e=>{if(confirm("Are you sure you want to delete this feed?"))try{(await fetch(`/api/rss/feeds/${e}`,{method:"DELETE"})).ok&&await j()}catch(e){console.error("Error deleting feed:",e)}};return x?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading RSS feeds..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h(),{href:"/",className:"mr-4",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-gray-600 hover:text-gray-900"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"RSS Feeds"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Manage your RSS feeds and view aggregated content"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{type:"button",onClick:w,disabled:u,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,r.jsx)(n.A,{className:`h-4 w-4 mr-2 ${u?"animate-spin":""}`}),"Refresh Content"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>g(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(l,{className:"h-4 w-4 mr-2"}),"Add Feed"]})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Configured Feeds"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[e.length," feeds configured"]})]}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:e.map(e=>(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:e.url})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>k(e.id,e.is_active),className:`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${e.is_active?"bg-blue-600":"bg-gray-200"}`,children:(0,r.jsx)("span",{className:`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${e.is_active?"translate-x-5":"translate-x-0"}`})}),(0,r.jsx)("button",{type:"button",onClick:()=>P(e.id),className:"text-red-600 hover:text-red-900",children:(0,r.jsx)(d,{className:"h-4 w-4"})})]})]})},e.id))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Latest Content"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[s.length," items found (deduplicated and scored)"]})]}),(0,r.jsx)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:s.map((e,t)=>(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500 space-x-4 mb-2",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(c,{className:"h-3 w-3 mr-1"}),new Date(e.publishedAt).toLocaleDateString()]}),e.author&&(0,r.jsxs)("span",{children:["by ",e.author]}),(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["Score: ",e.score.toFixed(0)]})]}),e.categories.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:e.categories.slice(0,3).map((e,t)=>(0,r.jsx)("span",{className:"inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded",children:e},t))}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 line-clamp-2",children:[e.content.slice(0,200),"..."]})]}),(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"ml-4 text-blue-600 hover:text-blue-900",children:(0,r.jsx)(m,{className:"h-4 w-4"})})]})},t))})]})})]})}),f&&(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add RSS Feed"}),(0,r.jsxs)("form",{onSubmit:N,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Feed Name"}),(0,r.jsx)("input",{type:"text",value:y.name,onChange:e=>v({...y,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Feed URL"}),(0,r.jsx)("input",{type:"url",value:y.url,onChange:e=>v({...y,url:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700",children:"Add Feed"})]})]})]})})})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,169,75,644],()=>s(29364));module.exports=r})();