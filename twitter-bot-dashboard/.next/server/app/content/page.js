(()=>{var e={};e.id=800,e.ids=[800],e.modules={100:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19713:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(60687),n=s(43210),a=s(78122),i=s(28559),o=s(62688);let l=(0,o.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),d=(0,o.A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);var c=s(85814),p=s.n(c);function h(){let[e,t]=(0,n.useState)([]),[s,o]=(0,n.useState)(new Set),[c,h]=(0,n.useState)(!0),[m,u]=(0,n.useState)(new Set),[x,b]=(0,n.useState)(!1),[g,f]=(0,n.useState)(4),y=async()=>{try{h(!0);let e=await fetch("/api/content/queue");if(e.ok){let s=await e.json();t(s);let r=new Set(s.filter(e=>e.is_selected).map(e=>e.id));o(r)}}catch(e){console.error("Error fetching content queue:",e)}finally{h(!1)}},v=async s=>{let r=e.find(e=>e.id===s);if(r)try{u(e=>new Set([...e,s]));let e=await fetch("/api/content/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:r.original_title,content:r.original_content,url:r.original_url})});if(e.ok){let r=await e.json();t(e=>e.map(e=>e.id===s?{...e,ai_generated_content:r.tweetContent,short_hook:r.shortHook,long_hook:r.longHook,personal_touch:r.personalTouch}:e))}}catch(e){console.error("Error generating content:",e)}finally{u(e=>{let t=new Set(e);return t.delete(s),t})}},j=async e=>{let t=new Set(s);if(t.has(e))t.delete(e);else{if(t.size>=g)return void alert(`You can only select up to ${g} topics at a time.`);t.add(e)}o(t);try{await fetch(`/api/content/queue/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_selected:t.has(e)})})}catch(e){console.error("Error updating selection:",e)}},w=async()=>{if(0===s.size)return void alert("Please select at least one item to post.");try{b(!0);let e=await fetch("/api/content/post",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contentIds:Array.from(s)})});if(e.ok){let t=await e.json();alert(`Successfully posted ${t.posted} out of ${s.size} selected items.`),await y(),o(new Set)}}catch(e){console.error("Error posting content:",e),alert("Error posting content. Please try again.")}finally{b(!1)}},N=async()=>{try{(await fetch("/api/content/refresh",{method:"POST"})).ok&&await y()}catch(e){console.error("Error refreshing queue:",e)}};return c?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(a.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading content queue..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p(),{href:"/",className:"mr-4",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-gray-600 hover:text-gray-900"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Content Queue"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Select and generate content for posting (",s.size,"/",g," selected)"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{type:"button",onClick:N,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(a.A,{className:"h-4 w-4 mr-2"}),"Refresh Queue"]}),(0,r.jsxs)("button",{type:"button",onClick:w,disabled:x||0===s.size,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[(0,r.jsx)(l,{className:`h-4 w-4 mr-2 ${x?"animate-pulse":""}`}),x?"Posting...":`Post Selected (${s.size})`]})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)("div",{className:`bg-white rounded-lg border-2 shadow-sm transition-all ${s.has(e.id)?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"} ${e.is_posted?"opacity-50":""}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 line-clamp-2 mb-2",children:e.original_title}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500 space-x-2",children:[(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded",children:["Score: ",e.priority_score.toFixed(0)]}),e.is_posted&&(0,r.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded",children:"Posted"})]})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.has(e.id),onChange:()=>j(e.id),disabled:e.is_posted,className:"ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("p",{className:"text-xs text-gray-600 line-clamp-3",children:[e.original_content.slice(0,200),"..."]})}),e.ai_generated_content?(0,r.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-xs font-medium text-blue-900 mb-2",children:"Generated Tweet:"}),(0,r.jsx)("p",{className:"text-sm text-blue-800",children:e.ai_generated_content}),e.short_hook&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("span",{className:"text-xs font-medium text-blue-700",children:"Hook: "}),(0,r.jsx)("span",{className:"text-xs text-blue-600",children:e.short_hook})]})]}):(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("button",{type:"button",onClick:()=>v(e.id),disabled:m.has(e.id),className:"w-full inline-flex items-center justify-center px-3 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,r.jsx)(d,{className:`h-4 w-4 mr-2 ${m.has(e.id)?"animate-spin":""}`}),m.has(e.id)?"Generating...":"Generate Content"]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsx)("a",{href:e.original_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:"View Original"}),(0,r.jsx)("span",{children:new Date(e.created_at).toLocaleDateString()})]})]})},e.id))}),0===e.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No content in queue. Refresh to load new content from RSS feeds."})})]})]})}},21514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35065:(e,t,s)=>{Promise.resolve().then(s.bind(s,21514))},39248:()=>{},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["content",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21514)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/content/page",pathname:"/content",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81104:()=>{},81540:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},87865:(e,t,s)=>{Promise.resolve().then(s.bind(s,19713))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(37413),n=s(2202),a=s.n(n),i=s(64988),o=s.n(i);s(61135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,169,75,644],()=>s(70276));module.exports=r})();