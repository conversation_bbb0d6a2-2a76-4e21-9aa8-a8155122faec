[{"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts": "1", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts": "2", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts": "3", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts": "4", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts": "5", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts": "6", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts": "7", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts": "8", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts": "9", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts": "10", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts": "11", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts": "12", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx": "13", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx": "14", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx": "15", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx": "16", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts": "17", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts": "18", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts": "19", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts": "20", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts": "21", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts": "22"}, {"size": 770, "mtime": 1751192507413, "results": "23", "hashOfConfig": "24"}, {"size": 2776, "mtime": 1751192527058, "results": "25", "hashOfConfig": "24"}, {"size": 577, "mtime": 1751192497229, "results": "26", "hashOfConfig": "24"}, {"size": 460, "mtime": 1751192488642, "results": "27", "hashOfConfig": "24"}, {"size": 2131, "mtime": 1751192542763, "results": "28", "hashOfConfig": "24"}, {"size": 581, "mtime": 1751192659471, "results": "29", "hashOfConfig": "24"}, {"size": 1166, "mtime": 1751192553340, "results": "30", "hashOfConfig": "24"}, {"size": 1057, "mtime": 1751192385082, "results": "31", "hashOfConfig": "24"}, {"size": 980, "mtime": 1751192373975, "results": "32", "hashOfConfig": "24"}, {"size": 1716, "mtime": 1751192399164, "results": "33", "hashOfConfig": "24"}, {"size": 1409, "mtime": 1751192629239, "results": "34", "hashOfConfig": "24"}, {"size": 3909, "mtime": 1751192302709, "results": "35", "hashOfConfig": "24"}, {"size": 12087, "mtime": 1751192460942, "results": "36", "hashOfConfig": "24"}, {"size": 12969, "mtime": 1751192362483, "results": "37", "hashOfConfig": "24"}, {"size": 689, "mtime": 1751190993312, "results": "38", "hashOfConfig": "24"}, {"size": 5738, "mtime": 1751192277182, "results": "39", "hashOfConfig": "24"}, {"size": 6636, "mtime": 1751192124554, "results": "40", "hashOfConfig": "24"}, {"size": 5285, "mtime": 1751192087032, "results": "41", "hashOfConfig": "24"}, {"size": 7708, "mtime": 1751192616092, "results": "42", "hashOfConfig": "24"}, {"size": 6111, "mtime": 1751192476538, "results": "43", "hashOfConfig": "24"}, {"size": 5762, "mtime": 1751192058079, "results": "44", "hashOfConfig": "24"}, {"size": 3102, "mtime": 1751192650475, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15v4ex5", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts", ["112"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts", ["113"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts", ["114"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts", ["115"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts", ["116"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts", ["117"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts", ["118"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/twitter/stats/route.ts", ["119", "120", "121"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx", ["122", "123", "124", "125"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx", ["126"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts", ["127", "128"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/twitter.ts", ["129"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts", ["130"], [], {"ruleId": "131", "severity": 2, "message": "132", "line": 4, "column": 27, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "132", "line": 5, "column": 28, "nodeType": null, "messageId": "133", "endLine": 5, "endColumn": 35}, {"ruleId": "131", "severity": 2, "message": "132", "line": 4, "column": 28, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 35}, {"ruleId": "131", "severity": 2, "message": "132", "line": 4, "column": 27, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "132", "line": 4, "column": 27, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "132", "line": 5, "column": 27, "nodeType": null, "messageId": "133", "endLine": 5, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "132", "line": 4, "column": 27, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "132", "line": 5, "column": 27, "nodeType": null, "messageId": "133", "endLine": 5, "endColumn": 34}, {"ruleId": "131", "severity": 2, "message": "134", "line": 29, "column": 11, "nodeType": null, "messageId": "133", "endLine": 29, "endColumn": 28}, {"ruleId": "131", "severity": 2, "message": "132", "line": 88, "column": 28, "nodeType": null, "messageId": "133", "endLine": 88, "endColumn": 35}, {"ruleId": "131", "severity": 2, "message": "135", "line": 4, "column": 45, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 48}, {"ruleId": "131", "severity": 2, "message": "136", "line": 4, "column": 50, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 58}, {"ruleId": "131", "severity": 2, "message": "137", "line": 4, "column": 60, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 73}, {"ruleId": "131", "severity": 2, "message": "138", "line": 4, "column": 75, "nodeType": null, "messageId": "133", "endLine": 4, "endColumn": 82}, {"ruleId": "139", "severity": 2, "message": "140", "line": 57, "column": 11, "nodeType": "141", "messageId": "142", "endLine": 57, "endColumn": 14, "suggestions": "143"}, {"ruleId": "131", "severity": 2, "message": "144", "line": 34, "column": 7, "nodeType": null, "messageId": "133", "endLine": 34, "endColumn": 22}, {"ruleId": "139", "severity": 2, "message": "140", "line": 118, "column": 68, "nodeType": "141", "messageId": "142", "endLine": 118, "endColumn": 71, "suggestions": "145"}, {"ruleId": "131", "severity": 2, "message": "146", "line": 141, "column": 13, "nodeType": null, "messageId": "133", "endLine": 141, "endColumn": 17}, {"ruleId": "131", "severity": 2, "message": "147", "line": 10, "column": 11, "nodeType": null, "messageId": "133", "endLine": 10, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "unusedVar", "'totalPostedTweets' is assigned a value but never used.", "'Eye' is defined but never used.", "'ThumbsUp' is defined but never used.", "'MessageCircle' is defined but never used.", "'Repeat2' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["148", "149"], "'includeHashtags' is assigned a value but never used.", ["150", "151"], "'user' is assigned a value but never used.", "'ExistingPostedData' is defined but never used.", {"messageId": "152", "fix": "153", "desc": "154"}, {"messageId": "155", "fix": "156", "desc": "157"}, {"messageId": "152", "fix": "158", "desc": "154"}, {"messageId": "155", "fix": "159", "desc": "157"}, "suggestUnknown", {"range": "160", "text": "161"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "162", "text": "163"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "164", "text": "161"}, {"range": "165", "text": "163"}, [1332, 1335], "unknown", [1332, 1335], "never", [3580, 3583], [3580, 3583]]