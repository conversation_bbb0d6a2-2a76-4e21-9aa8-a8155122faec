import { <PERSON><PERSON><PERSON> } from 'twitter-api-v2'

// Twitter client configuration
const twitterClient = new TwitterApi({
  clientId: process.env.TWITTER_CLIENT_ID!,
  clientSecret: process.env.TWITTER_CLIENT_SECRET!,
  accessToken: process.env.TWITTER_ACCESS_TOKEN!,
  refreshToken: process.env.TWITTER_REFRESH_TOKEN!,
})

// Twitter API v1.1 client for additional features
const twitterV1Client = new TwitterApi({
  appKey: process.env.TWITTER_APP_KEY!,
  appSecret: process.env.TWITTER_APP_SECRET!,
  accessToken: process.env.TWITTER_ACCESS_TOKEN!,
  accessSecret: process.env.TWITTER_ACCESS_SECRET!,
})

export interface TwitterUserData {
  id: string
  username: string
  name: string
  followers_count: number
  following_count: number
  tweet_count: number
  verified: boolean
  profile_image_url?: string
}

export interface TweetData {
  id: string
  text: string
  created_at: string
  public_metrics?: {
    retweet_count: number
    like_count: number
    reply_count: number
    quote_count: number
    impression_count?: number
  }
}

export const twitterOperations = {
  // Get current user information
  async getCurrentUser(): Promise<TwitterUserData> {
    try {
      const { client: rwClient } = await twitterClient.refreshOAuth2Token(
        process.env.TWITTER_REFRESH_TOKEN!
      )
      
      const user = await rwClient.v2.me({
        'user.fields': ['public_metrics', 'verified', 'profile_image_url']
      })
      
      return {
        id: user.data.id,
        username: user.data.username,
        name: user.data.name,
        followers_count: user.data.public_metrics?.followers_count || 0,
        following_count: user.data.public_metrics?.following_count || 0,
        tweet_count: user.data.public_metrics?.tweet_count || 0,
        verified: user.data.verified || false,
        profile_image_url: user.data.profile_image_url
      }
    } catch (error) {
      console.error('Error fetching current user:', error)
      throw error
    }
  },

  // Post a tweet
  async postTweet(content: string): Promise<TweetData> {
    try {
      const { client: rwClient, accessToken, refreshToken } = await twitterClient.refreshOAuth2Token(
        process.env.TWITTER_REFRESH_TOKEN!
      )
      
      // Update environment variables with new tokens
      process.env.TWITTER_ACCESS_TOKEN = accessToken
      process.env.TWITTER_REFRESH_TOKEN = refreshToken
      
      const tweet = await rwClient.v2.tweet(content)
      
      return {
        id: tweet.data.id,
        text: content,
        created_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error posting tweet:', error)
      throw error
    }
  },

  // Get recent tweets with metrics
  async getRecentTweets(count = 10): Promise<TweetData[]> {
    try {
      const user = await this.getCurrentUser()
      
      const tweets = await twitterClient.v2.userTimeline(user.id, {
        max_results: count,
        'tweet.fields': ['created_at', 'public_metrics']
      })
      
      return tweets.data?.map(tweet => ({
        id: tweet.id,
        text: tweet.text,
        created_at: tweet.created_at || new Date().toISOString(),
        public_metrics: tweet.public_metrics
      })) || []
    } catch (error) {
      console.error('Error fetching recent tweets:', error)
      throw error
    }
  },

  // Get tweet analytics
  async getTweetAnalytics(tweetId: string): Promise<TweetData | null> {
    try {
      const tweet = await twitterClient.v2.singleTweet(tweetId, {
        'tweet.fields': ['created_at', 'public_metrics']
      })
      
      if (!tweet.data) return null
      
      return {
        id: tweet.data.id,
        text: tweet.data.text,
        created_at: tweet.data.created_at || new Date().toISOString(),
        public_metrics: tweet.data.public_metrics
      }
    } catch (error) {
      console.error('Error fetching tweet analytics:', error)
      return null
    }
  },

  // Calculate total impressions from recent tweets
  async getTotalImpressions(days = 30): Promise<number> {
    try {
      const user = await this.getCurrentUser()
      const tweets = await this.getRecentTweets(100) // Get more tweets for better calculation
      
      // Filter tweets from the last N days
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      
      const recentTweets = tweets.filter(tweet => 
        new Date(tweet.created_at) >= cutoffDate
      )
      
      // Sum up impressions (if available) or estimate based on engagement
      let totalImpressions = 0
      for (const tweet of recentTweets) {
        if (tweet.public_metrics?.impression_count) {
          totalImpressions += tweet.public_metrics.impression_count
        } else if (tweet.public_metrics) {
          // Estimate impressions based on engagement (rough calculation)
          const engagement = (tweet.public_metrics.like_count || 0) + 
                           (tweet.public_metrics.retweet_count || 0) + 
                           (tweet.public_metrics.reply_count || 0)
          totalImpressions += Math.max(engagement * 10, 100) // Rough estimate
        }
      }
      
      return totalImpressions
    } catch (error) {
      console.error('Error calculating total impressions:', error)
      return 0
    }
  },

  // Refresh OAuth2 token
  async refreshToken(): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const { accessToken, refreshToken } = await twitterClient.refreshOAuth2Token(
        process.env.TWITTER_REFRESH_TOKEN!
      )
      
      return { accessToken, refreshToken }
    } catch (error) {
      console.error('Error refreshing token:', error)
      throw error
    }
  }
}

export { twitterClient, twitterV1Client }
