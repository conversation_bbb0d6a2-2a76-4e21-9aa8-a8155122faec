import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!
})

export interface ContentGenerationOptions {
  tone?: 'analytical' | 'casual' | 'professional' | 'enthusiastic' | 'critical'
  includePersonalTouch?: boolean
  maxLength?: number
  includeHashtags?: boolean
}

export interface GeneratedContent {
  shortHook: string
  longHook: string
  personalTouch: string
  tweetContent: string
  hashtags: string[]
}

export const aiOperations = {
  // Generate tweet content with hooks and personal touch
  async generateTweetContent(
    title: string,
    content: string,
    url: string,
    options: ContentGenerationOptions = {}
  ): Promise<GeneratedContent> {
    const {
      tone = 'analytical',
      includePersonalTouch = true,
      maxLength = 280,
      includeHashtags = true
    } = options

    try {
      const prompt = `
You are an expert social media content creator specializing in tech and startup content. 
Create engaging Twitter content based on this article:

Title: ${title}
Content: ${content.slice(0, 2000)}
URL: ${url}

Requirements:
1. Generate a SHORT HOOK (1-2 sentences, max 50 characters) that grabs attention
2. Generate a LONG HOOK (2-3 sentences, max 100 characters) that provides more context
3. Generate a PERSONAL TOUCH message that adds your unique perspective or insight
4. Create the final TWEET CONTENT (max ${maxLength} characters) combining the best elements
5. Suggest relevant hashtags

Tone: ${tone}
Include personal perspective: ${includePersonalTouch}

Format your response as JSON:
{
  "shortHook": "...",
  "longHook": "...",
  "personalTouch": "...",
  "tweetContent": "...",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3"]
}

Best practices for maximum engagement:
- Start with a compelling hook
- Use numbers, statistics, or surprising facts when available
- Ask questions or create curiosity gaps
- Include actionable insights
- Use power words and emotional triggers
- Keep it conversational and authentic
- Add your unique perspective or hot take
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 500,
        temperature: 0.7
      })

      const response = completion.choices[0].message.content?.trim()
      if (!response) throw new Error('No response from OpenAI')

      // Parse JSON response
      const parsedResponse = JSON.parse(response)
      
      // Validate and clean up the response
      return {
        shortHook: parsedResponse.shortHook || '',
        longHook: parsedResponse.longHook || '',
        personalTouch: parsedResponse.personalTouch || '',
        tweetContent: parsedResponse.tweetContent || '',
        hashtags: Array.isArray(parsedResponse.hashtags) ? parsedResponse.hashtags : []
      }
    } catch (error) {
      console.error('Error generating tweet content:', error)
      
      // Fallback: generate simple content
      return this.generateFallbackContent(title, content, url, options)
    }
  },

  // Generate multiple variations of tweet content
  async generateTweetVariations(
    title: string,
    content: string,
    url: string,
    count: number = 3,
    options: ContentGenerationOptions = {}
  ): Promise<GeneratedContent[]> {
    const variations: GeneratedContent[] = []
    
    for (let i = 0; i < count; i++) {
      try {
        const variation = await this.generateTweetContent(title, content, url, {
          ...options,
          tone: ['analytical', 'casual', 'enthusiastic'][i % 3] as any
        })
        variations.push(variation)
      } catch (error) {
        console.error(`Error generating variation ${i + 1}:`, error)
      }
    }
    
    return variations
  },

  // Generate content summary for preview
  async generateContentSummary(title: string, content: string): Promise<string> {
    try {
      const prompt = `
Summarize this article in 2-3 sentences, focusing on the key insights and takeaways:

Title: ${title}
Content: ${content.slice(0, 1500)}

Make it engaging and highlight what makes this newsworthy or interesting.
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 150,
        temperature: 0.5
      })

      return completion.choices[0].message.content?.trim() || 'Summary not available'
    } catch (error) {
      console.error('Error generating summary:', error)
      return 'Summary not available'
    }
  },

  // Analyze content for engagement potential
  async analyzeEngagementPotential(title: string, content: string): Promise<{
    score: number
    factors: string[]
    suggestions: string[]
  }> {
    try {
      const prompt = `
Analyze this content for Twitter engagement potential:

Title: ${title}
Content: ${content.slice(0, 1000)}

Rate the engagement potential (1-10) and provide:
1. Key factors that make it engaging or not
2. Suggestions to improve engagement

Format as JSON:
{
  "score": 7,
  "factors": ["factor1", "factor2"],
  "suggestions": ["suggestion1", "suggestion2"]
}
`

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 300,
        temperature: 0.3
      })

      const response = completion.choices[0].message.content?.trim()
      if (!response) throw new Error('No response from OpenAI')

      return JSON.parse(response)
    } catch (error) {
      console.error('Error analyzing engagement potential:', error)
      return {
        score: 5,
        factors: ['Unable to analyze'],
        suggestions: ['Try again later']
      }
    }
  },

  // Fallback content generation when AI fails
  generateFallbackContent(
    title: string,
    content: string,
    url: string,
    options: ContentGenerationOptions = {}
  ): GeneratedContent {
    const shortHook = title.slice(0, 50) + (title.length > 50 ? '...' : '')
    const longHook = `Interesting read: ${title.slice(0, 80)}${title.length > 80 ? '...' : ''}`
    const personalTouch = "Worth checking out this perspective on the latest tech developments."
    
    // Create basic tweet content
    const baseContent = `${shortHook}\n\n${personalTouch}\n\n${url}`
    const tweetContent = baseContent.length > (options.maxLength || 280) 
      ? `${title.slice(0, 200)}...\n\n${url}` 
      : baseContent

    return {
      shortHook,
      longHook,
      personalTouch,
      tweetContent,
      hashtags: ['tech', 'news', 'startup']
    }
  }
}

export default aiOperations
