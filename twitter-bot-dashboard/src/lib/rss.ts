import Parser from 'rss-parser'
import { extract } from '@extractus/article-extractor'

const parser = new Parser()

export interface RSSItem {
  title: string
  link: string
  pubDate?: string
  contentSnippet?: string
  content?: string
  guid?: string
  categories?: string[]
  author?: string
}

export interface ProcessedContent {
  title: string
  url: string
  content: string
  publishedAt: string
  categories: string[]
  author?: string
  guid: string
}

export const rssOperations = {
  // Parse RSS feed and return items
  async parseFeed(feedUrl: string): Promise<RSSItem[]> {
    try {
      const feed = await parser.parseURL(feedUrl)
      return feed.items.map(item => ({
        title: item.title || '',
        link: item.link || '',
        pubDate: item.pubDate,
        contentSnippet: item.contentSnippet,
        content: item.content,
        guid: item.guid || item.link || '',
        categories: item.categories || [],
        author: item.author
      }))
    } catch (error) {
      console.error(`Error parsing RSS feed ${feedUrl}:`, error)
      throw error
    }
  },

  // Extract full content from article URL
  async extractContent(url: string): Promise<string | null> {
    try {
      const article = await extract(url)
      return article?.content || null
    } catch (error) {
      console.error(`Error extracting content from ${url}:`, error)
      return null
    }
  },

  // Process RSS items and extract full content
  async processRSSItems(items: RSSItem[]): Promise<ProcessedContent[]> {
    const processedItems: ProcessedContent[] = []
    
    for (const item of items) {
      try {
        const fullContent = await this.extractContent(item.link)
        
        if (fullContent) {
          processedItems.push({
            title: item.title,
            url: item.link,
            content: fullContent,
            publishedAt: item.pubDate || new Date().toISOString(),
            categories: item.categories || [],
            author: item.author,
            guid: item.guid
          })
        }
      } catch (error) {
        console.error(`Error processing item ${item.title}:`, error)
        // Continue with next item
      }
    }
    
    return processedItems
  },

  // Deduplicate content based on URL and title similarity
  deduplicateContent(items: ProcessedContent[]): ProcessedContent[] {
    const seen = new Set<string>()
    const deduplicated: ProcessedContent[] = []
    
    for (const item of items) {
      // Create a key based on URL and normalized title
      const normalizedTitle = item.title.toLowerCase().replace(/[^\w\s]/g, '').trim()
      const key = `${item.url}|${normalizedTitle}`
      
      if (!seen.has(key)) {
        seen.add(key)
        deduplicated.push(item)
      }
    }
    
    return deduplicated
  },

  // Get content from multiple RSS feeds and deduplicate
  async getAggregatedContent(feedUrls: string[]): Promise<ProcessedContent[]> {
    const allItems: ProcessedContent[] = []
    
    for (const feedUrl of feedUrls) {
      try {
        const items = await this.parseFeed(feedUrl)
        const processedItems = await this.processRSSItems(items)
        allItems.push(...processedItems)
      } catch (error) {
        console.error(`Error processing feed ${feedUrl}:`, error)
        // Continue with other feeds
      }
    }
    
    // Deduplicate and sort by publication date
    const deduplicated = this.deduplicateContent(allItems)
    return deduplicated.sort((a, b) => 
      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    )
  },

  // Filter content by keywords or categories
  filterContent(items: ProcessedContent[], keywords: string[] = []): ProcessedContent[] {
    if (keywords.length === 0) return items
    
    return items.filter(item => {
      const searchText = `${item.title} ${item.content} ${item.categories.join(' ')}`.toLowerCase()
      return keywords.some(keyword => 
        searchText.includes(keyword.toLowerCase())
      )
    })
  },

  // Score content based on various factors
  scoreContent(items: ProcessedContent[]): (ProcessedContent & { score: number })[] {
    return items.map(item => {
      let score = 0
      
      // Recency score (newer content gets higher score)
      const ageInHours = (Date.now() - new Date(item.publishedAt).getTime()) / (1000 * 60 * 60)
      score += Math.max(0, 100 - ageInHours) // Max 100 points for very recent content
      
      // Title quality score
      const titleWords = item.title.split(' ').length
      if (titleWords >= 5 && titleWords <= 15) score += 20 // Optimal title length
      
      // Content length score
      const contentWords = item.content.split(' ').length
      if (contentWords >= 200 && contentWords <= 2000) score += 30 // Good content length
      
      // Category relevance (tech-related categories get bonus)
      const techKeywords = ['tech', 'technology', 'ai', 'startup', 'innovation', 'software', 'app']
      const hasRelevantCategory = item.categories.some(cat => 
        techKeywords.some(keyword => cat.toLowerCase().includes(keyword))
      )
      if (hasRelevantCategory) score += 25
      
      return { ...item, score }
    }).sort((a, b) => b.score - a.score)
  }
}

export default rssOperations
