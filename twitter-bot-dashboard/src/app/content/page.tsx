'use client'

import { useEffect, useState } from 'react'
import { ArrowLeft, RefreshCw, Wand2, Send, Eye, ThumbsUp, MessageCircle, Repeat2 } from 'lucide-react'
import Link from 'next/link'

interface ContentItem {
  id: string
  original_url: string
  original_title: string
  original_content: string
  ai_generated_content?: string
  short_hook?: string
  long_hook?: string
  personal_touch?: string
  is_selected: boolean
  is_posted: boolean
  priority_score: number
  created_at: string
}

interface GeneratedContent {
  shortHook: string
  longHook: string
  personalTouch: string
  tweetContent: string
  hashtags: string[]
}

export default function ContentPage() {
  const [contentQueue, setContentQueue] = useState<ContentItem[]>([])
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState<Set<string>>(new Set())
  const [posting, setPosting] = useState(false)
  const [maxTopics, setMaxTopics] = useState(4)

  const fetchContentQueue = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/content/queue')
      if (response.ok) {
        const data = await response.json()
        setContentQueue(data)
        
        // Initialize selected items from database
        const selected = new Set(
          data.filter((item: ContentItem) => item.is_selected).map((item: ContentItem) => item.id)
        )
        setSelectedItems(selected)
      }
    } catch (error) {
      console.error('Error fetching content queue:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateContent = async (itemId: string) => {
    const item = contentQueue.find(i => i.id === itemId)
    if (!item) return

    try {
      setGenerating(prev => new Set([...prev, itemId]))
      
      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: item.original_title,
          content: item.original_content,
          url: item.original_url
        })
      })
      
      if (response.ok) {
        const generatedContent: GeneratedContent = await response.json()
        
        // Update the item in the queue
        setContentQueue(prev => prev.map(i => 
          i.id === itemId 
            ? {
                ...i,
                ai_generated_content: generatedContent.tweetContent,
                short_hook: generatedContent.shortHook,
                long_hook: generatedContent.longHook,
                personal_touch: generatedContent.personalTouch
              }
            : i
        ))
      }
    } catch (error) {
      console.error('Error generating content:', error)
    } finally {
      setGenerating(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    }
  }

  const toggleSelection = async (itemId: string) => {
    const newSelected = new Set(selectedItems)
    
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      // Enforce max topics limit
      if (newSelected.size >= maxTopics) {
        alert(`You can only select up to ${maxTopics} topics at a time.`)
        return
      }
      newSelected.add(itemId)
    }
    
    setSelectedItems(newSelected)
    
    // Update in database
    try {
      await fetch(`/api/content/queue/${itemId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_selected: newSelected.has(itemId) })
      })
    } catch (error) {
      console.error('Error updating selection:', error)
    }
  }

  const postSelectedContent = async () => {
    if (selectedItems.size === 0) {
      alert('Please select at least one item to post.')
      return
    }

    try {
      setPosting(true)
      const response = await fetch('/api/content/post', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          contentIds: Array.from(selectedItems) 
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        alert(`Successfully posted ${result.posted} out of ${selectedItems.size} selected items.`)
        
        // Refresh the queue
        await fetchContentQueue()
        setSelectedItems(new Set())
      }
    } catch (error) {
      console.error('Error posting content:', error)
      alert('Error posting content. Please try again.')
    } finally {
      setPosting(false)
    }
  }

  const refreshQueue = async () => {
    try {
      const response = await fetch('/api/content/refresh', {
        method: 'POST'
      })
      
      if (response.ok) {
        await fetchContentQueue()
      }
    } catch (error) {
      console.error('Error refreshing queue:', error)
    }
  }

  useEffect(() => {
    fetchContentQueue()
    
    // Fetch user preferences for max topics
    fetch('/api/preferences')
      .then(res => res.json())
      .then(data => {
        if (data.max_topics_to_select) {
          setMaxTopics(data.max_topics_to_select)
        }
      })
      .catch(console.error)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading content queue...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link href="/" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Content Queue</h1>
                <p className="text-sm text-gray-600 mt-1">
                  Select and generate content for posting ({selectedItems.size}/{maxTopics} selected)
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={refreshQueue}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Queue
              </button>
              <button
                type="button"
                onClick={postSelectedContent}
                disabled={posting || selectedItems.size === 0}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                <Send className={`h-4 w-4 mr-2 ${posting ? 'animate-pulse' : ''}`} />
                {posting ? 'Posting...' : `Post Selected (${selectedItems.size})`}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {contentQueue.map((item) => (
            <div
              key={item.id}
              className={`bg-white rounded-lg border-2 shadow-sm transition-all ${
                selectedItems.has(item.id)
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              } ${item.is_posted ? 'opacity-50' : ''}`}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 mb-2">
                      {item.original_title}
                    </h3>
                    <div className="flex items-center text-xs text-gray-500 space-x-2">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        Score: {item.priority_score.toFixed(0)}
                      </span>
                      {item.is_posted && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                          Posted
                        </span>
                      )}
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => toggleSelection(item.id)}
                    disabled={item.is_posted}
                    className="ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>

                {/* Original Content Preview */}
                <div className="mb-4">
                  <p className="text-xs text-gray-600 line-clamp-3">
                    {item.original_content.slice(0, 200)}...
                  </p>
                </div>

                {/* AI Generated Content */}
                {item.ai_generated_content ? (
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <h4 className="text-xs font-medium text-blue-900 mb-2">Generated Tweet:</h4>
                    <p className="text-sm text-blue-800">{item.ai_generated_content}</p>
                    
                    {item.short_hook && (
                      <div className="mt-2">
                        <span className="text-xs font-medium text-blue-700">Hook: </span>
                        <span className="text-xs text-blue-600">{item.short_hook}</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="mb-4">
                    <button
                      type="button"
                      onClick={() => generateContent(item.id)}
                      disabled={generating.has(item.id)}
                      className="w-full inline-flex items-center justify-center px-3 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      <Wand2 className={`h-4 w-4 mr-2 ${generating.has(item.id) ? 'animate-spin' : ''}`} />
                      {generating.has(item.id) ? 'Generating...' : 'Generate Content'}
                    </button>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <a
                    href={item.original_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View Original
                  </a>
                  <span>{new Date(item.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {contentQueue.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No content in queue. Refresh to load new content from RSS feeds.</p>
          </div>
        )}
      </div>
    </div>
  )
}
