import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const updates = await request.json()
    const updatedItem = await dbOperations.updateContentQueue(params.id, updates)
    return NextResponse.json(updatedItem)
  } catch (error) {
    console.error('Error updating content queue item:', error)
    return NextResponse.json(
      { error: 'Failed to update content queue item' },
      { status: 500 }
    )
  }
}
