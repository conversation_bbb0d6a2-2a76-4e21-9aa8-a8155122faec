import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const contentQueue = await dbOperations.getContentQueue(50)
    return NextResponse.json(contentQueue)
  } catch (error) {
    console.error('Error fetching content queue:', error)
    return NextResponse.json(
      { error: 'Failed to fetch content queue' },
      { status: 500 }
    )
  }
}
