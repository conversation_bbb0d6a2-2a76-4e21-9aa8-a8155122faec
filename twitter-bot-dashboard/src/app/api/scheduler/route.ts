import { NextRequest, NextResponse } from 'next/server'
import twitterBotScheduler from '@/lib/scheduler'

export async function GET(request: NextRequest) {
  try {
    const status = twitterBotScheduler.getStatus()
    return NextResponse.json(status)
  } catch (error) {
    console.error('Error getting scheduler status:', error)
    return NextResponse.json(
      { error: 'Failed to get scheduler status' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()
    
    if (action === 'start') {
      await twitterBotScheduler.start()
      return NextResponse.json({ message: 'Scheduler started successfully' })
    } else if (action === 'stop') {
      twitterBotScheduler.stop()
      return NextResponse.json({ message: 'Scheduler stopped successfully' })
    } else if (action === 'process') {
      // Manual trigger for testing
      await twitterBotScheduler.processAutomatedPosting()
      return NextResponse.json({ message: 'Manual posting process completed' })
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "start", "stop", or "process"' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error controlling scheduler:', error)
    return NextResponse.json(
      { error: 'Failed to control scheduler' },
      { status: 500 }
    )
  }
}
