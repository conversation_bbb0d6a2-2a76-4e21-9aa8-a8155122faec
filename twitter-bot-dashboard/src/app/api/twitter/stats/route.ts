import { NextResponse } from 'next/server'
import { twitterOperations } from '@/lib/twitter'
import { dbOperations } from '@/lib/supabase'

export async function GET() {
  try {
    // Get current user data from Twitter
    const userData = await twitterOperations.getCurrentUser()
    
    // Get recent tweets for engagement calculation
    const recentTweets = await twitterOperations.getRecentTweets(50)
    
    // Calculate total engagements from recent tweets
    let totalEngagements = 0
    for (const tweet of recentTweets) {
      if (tweet.public_metrics) {
        totalEngagements += (tweet.public_metrics.like_count || 0) +
                           (tweet.public_metrics.retweet_count || 0) +
                           (tweet.public_metrics.reply_count || 0) +
                           (tweet.public_metrics.quote_count || 0)
      }
    }
    
    // Get total impressions (estimated)
    const totalImpressions = await twitterOperations.getTotalImpressions(30)
    
    // Get posted tweets count from database
    const postedTweets = await dbOperations.getPostedTweets(1000)
    
    // Calculate database-stored impressions
    const dbImpressions = postedTweets.reduce((sum, tweet) => sum + tweet.impressions, 0)
    
    // Store current analytics in database
    await dbOperations.addAnalytics({
      followers_count: userData.followers_count,
      following_count: userData.following_count,
      total_tweets: userData.tweet_count,
      total_impressions: Math.max(totalImpressions, dbImpressions),
      total_engagements: totalEngagements
    })
    
    const stats = {
      followers: userData.followers_count,
      totalTweets: userData.tweet_count,
      totalImpressions: Math.max(totalImpressions, dbImpressions),
      totalEngagements: totalEngagements,
      recentTweets: recentTweets.length,
      username: userData.username,
      name: userData.name,
      verified: userData.verified,
      profileImage: userData.profile_image_url
    }
    
    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching Twitter stats:', error)
    
    // Return fallback data from database if Twitter API fails
    try {
      const latestAnalytics = await dbOperations.getLatestAnalytics()
      const postedTweets = await dbOperations.getPostedTweets(1000)
      
      if (latestAnalytics) {
        return NextResponse.json({
          followers: latestAnalytics.followers_count,
          totalTweets: latestAnalytics.total_tweets,
          totalImpressions: latestAnalytics.total_impressions,
          totalEngagements: latestAnalytics.total_engagements,
          recentTweets: postedTweets.length,
          username: 'N/A',
          name: 'N/A',
          verified: false,
          profileImage: null
        })
      }
    } catch (dbError) {
      console.error('Error fetching fallback data:', dbError)
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch Twitter stats' },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    // Force refresh of Twitter stats
    const userData = await twitterOperations.getCurrentUser()
    const totalImpressions = await twitterOperations.getTotalImpressions(30)
    
    // Update analytics in database
    await dbOperations.addAnalytics({
      followers_count: userData.followers_count,
      following_count: userData.following_count,
      total_tweets: userData.tweet_count,
      total_impressions: totalImpressions,
      total_engagements: 0 // Will be calculated separately
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error refreshing Twitter stats:', error)
    return NextResponse.json(
      { error: 'Failed to refresh Twitter stats' },
      { status: 500 }
    )
  }
}
