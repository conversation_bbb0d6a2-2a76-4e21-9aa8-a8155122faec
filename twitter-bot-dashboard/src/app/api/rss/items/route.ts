import { NextRequest, NextResponse } from 'next/server'
import { dbOperations } from '@/lib/supabase'
import rssOperations from '@/lib/rss'

export async function GET(request: NextRequest) {
  try {
    // Get active RSS feeds from database
    const feeds = await dbOperations.getRSSFeeds()
    const activeFeeds = feeds.filter(feed => feed.is_active)
    
    if (activeFeeds.length === 0) {
      return NextResponse.json([])
    }
    
    // Get aggregated content from all active feeds
    const feedUrls = activeFeeds.map(feed => feed.url)
    const content = await rssOperations.getAggregatedContent(feedUrls)
    
    // Score and sort content
    const scoredContent = rssOperations.scoreContent(content)
    
    // Return top 50 items
    return NextResponse.json(scoredContent.slice(0, 50))
  } catch (error) {
    console.error('Error fetching RSS items:', error)
    return NextResponse.json(
      { error: 'Failed to fetch RSS items' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { feedUrl, limit = 10 } = await request.json()
    
    if (!feedUrl) {
      return NextResponse.json(
        { error: 'Feed URL is required' },
        { status: 400 }
      )
    }
    
    // Parse single feed
    const items = await rssOperations.parseFeed(feedUrl)
    const processedItems = await rssOperations.processRSSItems(items.slice(0, limit))
    const scoredItems = rssOperations.scoreContent(processedItems)
    
    return NextResponse.json(scoredItems)
  } catch (error) {
    console.error('Error processing RSS feed:', error)
    return NextResponse.json(
      { error: 'Failed to process RSS feed' },
      { status: 500 }
    )
  }
}
