/**
 * Migration script to integrate existing Twitter bot logic with the new dashboard
 * This script helps migrate data from the existing bot to the new Supabase database
 */

import fs from 'fs'
import path from 'path'
import { dbOperations } from '../lib/supabase'

interface ExistingPostedData {
  url: string
  title?: string
  postedAt?: string
}

export async function migrateExistingData() {
  try {
    console.log('Starting migration of existing Twitter bot data...')
    
    // Check if posted.json exists in the parent directory
    const postedFilePath = path.join(process.cwd(), '..', 'posted.json')
    
    if (fs.existsSync(postedFilePath)) {
      console.log('Found existing posted.json file')
      
      const postedData = JSON.parse(fs.readFileSync(postedFilePath, 'utf8'))
      
      if (Array.isArray(postedData)) {
        console.log(`Found ${postedData.length} posted URLs`)
        
        // Migrate posted URLs to database
        for (const url of postedData) {
          try {
            await dbOperations.addPostedTweet({
              content: `Migrated from existing bot: ${url}`,
              original_url: url,
              original_title: 'Migrated Content',
              impressions: 0,
              retweets: 0,
              likes: 0,
              replies: 0,
              posted_at: new Date().toISOString()
            })
          } catch (error) {
            // Skip duplicates
            if (!error.message?.includes('duplicate')) {
              console.error(`Error migrating URL ${url}:`, error)
            }
          }
        }
        
        console.log('Migration of posted URLs completed')
      }
    } else {
      console.log('No existing posted.json file found')
    }
    
    // Migrate RSS feeds from existing configuration
    const existingFeeds = [
      { name: 'TechCrunch', url: 'https://techcrunch.com/feed/' },
      { name: 'The Verge', url: 'https://www.theverge.com/rss/index.xml' },
      { name: 'Crunchbase News', url: 'https://news.crunchbase.com/feed/' },
      { name: 'Android Police', url: 'https://www.androidpolice.com/feed/' }
    ]
    
    console.log('Ensuring default RSS feeds are configured...')
    
    for (const feed of existingFeeds) {
      try {
        await dbOperations.addRSSFeed(feed.name, feed.url)
        console.log(`Added RSS feed: ${feed.name}`)
      } catch (error) {
        // Skip duplicates
        if (!error.message?.includes('duplicate')) {
          console.error(`Error adding RSS feed ${feed.name}:`, error)
        }
      }
    }
    
    console.log('Migration completed successfully!')
    
  } catch (error) {
    console.error('Error during migration:', error)
    throw error
  }
}

// Export for use in API routes or manual execution
export default migrateExistingData

// Allow direct execution
if (require.main === module) {
  migrateExistingData()
    .then(() => {
      console.log('Migration script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Migration script failed:', error)
      process.exit(1)
    })
}
