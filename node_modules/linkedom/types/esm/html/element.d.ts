/**
 * @implements globalThis.HTMLElement
 */
export class HTMLElement extends Element implements globalThis.HTMLElement {
    static get observedAttributes(): any[];
    constructor(ownerDocument?: any, localName?: string);
    blur(): void;
    click(): void;
    get accessKeyLabel(): string;
    get isContentEditable(): boolean;
    set contentEditable(value: any);
    get contentEditable(): any;
    set draggable(value: any);
    get draggable(): any;
    set hidden(value: any);
    get hidden(): any;
    set spellcheck(value: any);
    get spellcheck(): any;
    set accessKey(value: any);
    get accessKey(): any;
    set dir(value: any);
    get dir(): any;
    set lang(value: any);
    get lang(): any;
    set title(value: any);
    get title(): any;
    set onabort(value: any);
    get onabort(): any;
    set onblur(value: any);
    get onblur(): any;
    set oncancel(value: any);
    get oncancel(): any;
    set oncanplay(value: any);
    get oncanplay(): any;
    set oncanplaythrough(value: any);
    get oncanplaythrough(): any;
    set onchange(value: any);
    get onchange(): any;
    set onclick(value: any);
    get onclick(): any;
    set onclose(value: any);
    get onclose(): any;
    set oncontextmenu(value: any);
    get oncontextmenu(): any;
    set oncuechange(value: any);
    get oncuechange(): any;
    set ondblclick(value: any);
    get ondblclick(): any;
    set ondrag(value: any);
    get ondrag(): any;
    set ondragend(value: any);
    get ondragend(): any;
    set ondragenter(value: any);
    get ondragenter(): any;
    set ondragleave(value: any);
    get ondragleave(): any;
    set ondragover(value: any);
    get ondragover(): any;
    set ondragstart(value: any);
    get ondragstart(): any;
    set ondrop(value: any);
    get ondrop(): any;
    set ondurationchange(value: any);
    get ondurationchange(): any;
    set onemptied(value: any);
    get onemptied(): any;
    set onended(value: any);
    get onended(): any;
    set onerror(value: any);
    get onerror(): any;
    set onfocus(value: any);
    get onfocus(): any;
    set oninput(value: any);
    get oninput(): any;
    set oninvalid(value: any);
    get oninvalid(): any;
    set onkeydown(value: any);
    get onkeydown(): any;
    set onkeypress(value: any);
    get onkeypress(): any;
    set onkeyup(value: any);
    get onkeyup(): any;
    set onload(value: any);
    get onload(): any;
    set onloadeddata(value: any);
    get onloadeddata(): any;
    set onloadedmetadata(value: any);
    get onloadedmetadata(): any;
    set onloadstart(value: any);
    get onloadstart(): any;
    set onmousedown(value: any);
    get onmousedown(): any;
    set onmouseenter(value: any);
    get onmouseenter(): any;
    set onmouseleave(value: any);
    get onmouseleave(): any;
    set onmousemove(value: any);
    get onmousemove(): any;
    set onmouseout(value: any);
    get onmouseout(): any;
    set onmouseover(value: any);
    get onmouseover(): any;
    set onmouseup(value: any);
    get onmouseup(): any;
    set onmousewheel(value: any);
    get onmousewheel(): any;
    set onpause(value: any);
    get onpause(): any;
    set onplay(value: any);
    get onplay(): any;
    set onplaying(value: any);
    get onplaying(): any;
    set onprogress(value: any);
    get onprogress(): any;
    set onratechange(value: any);
    get onratechange(): any;
    set onreset(value: any);
    get onreset(): any;
    set onresize(value: any);
    get onresize(): any;
    set onscroll(value: any);
    get onscroll(): any;
    set onseeked(value: any);
    get onseeked(): any;
    set onseeking(value: any);
    get onseeking(): any;
    set onselect(value: any);
    get onselect(): any;
    set onshow(value: any);
    get onshow(): any;
    set onstalled(value: any);
    get onstalled(): any;
    set onsubmit(value: any);
    get onsubmit(): any;
    set onsuspend(value: any);
    get onsuspend(): any;
    set ontimeupdate(value: any);
    get ontimeupdate(): any;
    set ontoggle(value: any);
    get ontoggle(): any;
    set onvolumechange(value: any);
    get onvolumechange(): any;
    set onwaiting(value: any);
    get onwaiting(): any;
    set onauxclick(value: any);
    get onauxclick(): any;
    set ongotpointercapture(value: any);
    get ongotpointercapture(): any;
    set onlostpointercapture(value: any);
    get onlostpointercapture(): any;
    set onpointercancel(value: any);
    get onpointercancel(): any;
    set onpointerdown(value: any);
    get onpointerdown(): any;
    set onpointerenter(value: any);
    get onpointerenter(): any;
    set onpointerleave(value: any);
    get onpointerleave(): any;
    set onpointermove(value: any);
    get onpointermove(): any;
    set onpointerout(value: any);
    get onpointerout(): any;
    set onpointerover(value: any);
    get onpointerover(): any;
    set onpointerup(value: any);
    get onpointerup(): any;
}
import { Element } from '../interface/element.js';
