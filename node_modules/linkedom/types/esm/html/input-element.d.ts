/**
 * @implements globalThis.HTMLInputElement
 */
export class HTMLInputElement extends HTMLElement implements globalThis.HTMLInputElement {
    set autofocus(value: any);
    get autofocus(): any;
    set disabled(value: any);
    get disabled(): any;
    set name(value: any);
    get name(): any;
    set placeholder(value: any);
    get placeholder(): any;
    set type(value: any);
    get type(): any;
    set value(value: any);
    get value(): any;
}
import { HTMLElement } from './element.js';
