/**
 * @implements globalThis.HTMLLinkElement
 */
export class HTMLLinkElement extends HTMLElement implements globalThis.HTMLLinkElement {
    set disabled(value: any);
    get disabled(): any;
    set href(value: any);
    get href(): any;
    set hreflang(value: any);
    get hreflang(): any;
    set media(value: any);
    get media(): any;
    set rel(value: any);
    get rel(): any;
    set type(value: any);
    get type(): any;
}
import { HTMLElement } from './element.js';
