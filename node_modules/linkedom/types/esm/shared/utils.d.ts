export { $String as String };
export function getEnd(node: any): any;
export function ignoreCase({ ownerDocument }: {
    ownerDocument: any;
}): any;
export function knownAdjacent(prev: any, next: any): void;
export function knownBoundaries(prev: any, current: any, next: any): void;
export function knownSegment(prev: any, start: any, end: any, next: any): void;
export function knownSiblings(prev: any, current: any, next: any): void;
export function localCase({ localName, ownerDocument }: {
    localName: any;
    ownerDocument: any;
}): any;
export function setAdjacent(prev: any, next: any): void;
export function htmlToFragment(ownerDocument: import("../interface/document.js").Document, html: string): import("../interface/document-fragment.js").DocumentFragment;
declare const $String: StringConstructor;
