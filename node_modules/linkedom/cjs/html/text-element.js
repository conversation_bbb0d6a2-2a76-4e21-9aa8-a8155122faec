'use strict';
const {HTMLElement} = require('./element.js');

const {toString} = HTMLElement.prototype;

class TextElement extends HTMLElement {

  get innerHTML() { return this.textContent; }
  set innerHTML(html) { this.textContent = html; }

  toString() {
    const outerHTML = toString.call(this.cloneNode());
    return outerHTML.replace('><', () => `>${this.textContent}<`);
  }
}
exports.TextElement = TextElement
