{"version": "8.0.19", "name": "@extractus/article-extractor", "description": "To extract main article from given URL", "homepage": "https://github.com/extractus/article-extractor", "repository": {"type": "git", "url": "**************:extractus/article-extractor.git"}, "author": "@extractus", "main": "./src/main.js", "type": "module", "imports": {"cross-fetch": "./src/deno/cross-fetch.js"}, "browser": {"cross-fetch": "./src/deno/cross-fetch.js", "linkedom": "./src/browser/linkedom.js"}, "types": "./index.d.ts", "engines": {"node": ">= 18"}, "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix .", "pretest": "npm run lint", "test": "node --test --experimental-test-coverage", "eval": "node eval", "reset": "node reset"}, "dependencies": {"@mozilla/readability": "^0.6.0", "@ndaidong/bellajs": "^12.0.1", "cross-fetch": "^4.1.0", "linkedom": "^0.18.10", "sanitize-html": "2.16.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/sanitize-html": "^2.16.0", "eslint": "^9.26.0", "globals": "^16.1.0", "https-proxy-agent": "^7.0.6", "nock": "^14.0.4"}, "keywords": ["article", "extractor", "parser", "readability", "util"], "license": "MIT"}