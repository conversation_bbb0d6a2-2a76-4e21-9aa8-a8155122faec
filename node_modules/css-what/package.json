{"name": "css-what", "version": "6.2.1", "description": "a CSS selector parser", "repository": {"type": "git", "url": "https://github.com/fb55/css-what"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>> (http://feedic.com)", "sideEffects": false, "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["dist", "src"], "scripts": {"format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:tsc && npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "lint:tsc": "tsc --noEmit", "prepublishOnly": "tshy", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:vi && npm run lint", "test:vi": "vitest run"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^55.0.0", "prettier": "^3.6.2", "tshy": "^3.0.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "engines": {"node": ">= 6"}, "tshy": {"exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}}