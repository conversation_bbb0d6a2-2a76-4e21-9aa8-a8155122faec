{"": [], "\t": [], "\t#qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "\n#qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "\f#qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "\r#qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], " ": [], " #qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], " a ": [[{"type": "tag", "name": "a", "namespace": null}]], " p ": [[{"type": "tag", "name": "p", "namespace": null}]], "#__sizzle__": [[{"type": "attribute", "name": "id", "action": "equals", "value": "__sizzle__", "namespace": null, "ignoreCase": "quirks"}]], "#ap :nth-last-of-type(0n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "0n+3"}]], "#ap :nth-last-of-type(2n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "2n"}]], "#ap :nth-last-of-type(2n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "2n+1"}]], "#ap :nth-last-of-type(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "3"}]], "#ap :nth-last-of-type(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "even"}]], "#ap :nth-last-of-type(n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "n"}]], "#ap :nth-last-of-type(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "odd"}]], "#ap :nth-of-type(0n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "0n+3"}]], "#ap :nth-of-type(2n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "2n"}]], "#ap :nth-of-type(2n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "2n+1"}]], "#ap :nth-of-type(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "3"}]], "#ap :nth-of-type(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "even"}]], "#ap :nth-of-type(n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "n"}]], "#ap :nth-of-type(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-of-type", "data": "odd"}]], "#ap a[hreflang!='en']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "hreflang", "action": "not", "value": "en", "namespace": null, "ignoreCase": null}]], "#ap:has(*), #ap:has(*)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}], [{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]], "#asdfasdf #foobar": [[{"type": "attribute", "name": "id", "action": "equals", "value": "asdfasdf", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "foobar", "namespace": null, "ignoreCase": "quirks"}]], "#attr-child-boosh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}]], "#attributes a[href=\"#aname\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "equals", "value": "#aname", "namespace": null, "ignoreCase": null}]], "#attributes div[test$=foo]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "end", "value": "foo", "namespace": null, "ignoreCase": null}]], "#attributes div[test*=hree]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "any", "value": "hree", "namespace": null, "ignoreCase": null}]], "#attributes div[test=\"two-foo\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "equals", "value": "two-foo", "namespace": null, "ignoreCase": null}]], "#attributes div[test='two-foo']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "equals", "value": "two-foo", "namespace": null, "ignoreCase": null}]], "#attributes div[test=two-foo]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "equals", "value": "two-foo", "namespace": null, "ignoreCase": null}]], "#attributes div[test^=two]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "start", "value": "two", "namespace": null, "ignoreCase": null}]], "#attributes div[test|=\"two-foo\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "hyphen", "value": "two-foo", "namespace": null, "ignoreCase": null}]], "#attributes div[test|=two]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "hyphen", "value": "two", "namespace": null, "ignoreCase": null}]], "#attributes div[test~=three]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "element", "value": "three", "namespace": null, "ignoreCase": null}]], "#attributes div[unique-test]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "attributes", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "unique-test", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#backslash\\\\foo": [[{"type": "attribute", "name": "id", "action": "equals", "value": "backslash\\foo", "namespace": null, "ignoreCase": "quirks"}]], "#blargh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]], "#body": [[{"type": "attribute", "name": "id", "action": "equals", "value": "body", "namespace": null, "ignoreCase": "quirks"}]], "#boosh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}]], "#boosh #booshTest": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], "#boosh *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}]], "#boosh .a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}]], "#boosh div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "#boosh div div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "#boosh div,#boosh span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#boosh div.a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}]], "#boosh div[test=fg]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "equals", "value": "fg", "namespace": null, "ignoreCase": null}]], "#boosh div[test]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "test", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#boosh span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#boosh,#boosh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}], [{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}]], "#boosh,.apples,#boosh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}], [{"type": "attribute", "name": "class", "action": "element", "value": "apples", "namespace": null, "ignoreCase": "quirks"}], [{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}]], "#boosh>.a>#booshTest": [[{"type": "attribute", "name": "id", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], "#booshTest": [[{"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], "#direct-descend > .direct-descend": [[{"type": "attribute", "name": "id", "action": "equals", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}]], "#direct-descend > .direct-descend > .lvl2": [[{"type": "attribute", "name": "id", "action": "equals", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "lvl2", "namespace": null, "ignoreCase": "quirks"}]], "#dupContainer span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#dupL1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "dupL1", "namespace": null, "ignoreCase": "quirks"}]], "#dupL2": [[{"type": "attribute", "name": "id", "action": "equals", "value": "dupL2", "namespace": null, "ignoreCase": "quirks"}]], "#emem": [[{"type": "attribute", "name": "id", "action": "equals", "value": "emem", "namespace": null, "ignoreCase": "quirks"}]], "#first ~ div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "first", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "div", "namespace": null}]], "#firstUL > *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "firstUL", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}]], "#firstp #foobar": [[{"type": "attribute", "name": "id", "action": "equals", "value": "firstp", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "foobar", "namespace": null, "ignoreCase": "quirks"}]], "#firstp #simon1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "firstp", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "simon1", "namespace": null, "ignoreCase": "quirks"}]], "#fixtures": [[{"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}]], "#fixtures a *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "descendant"}, {"type": "universal", "namespace": null}]], "#fixtures h1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "h1", "namespace": null}]], "#foo": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]], "#foo > *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}]], "#foo a:not(.blog)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}]]}]], "#foo a:not(.blog.link)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "link", "namespace": null, "ignoreCase": "quirks"}]]}]], "#foo a:not(.link)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "link", "namespace": null, "ignoreCase": "quirks"}]]}]], "#foo\\:bar": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], "#foo\\:bar span:not(:input)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "input", "data": null}]]}]], "#form": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}]], "#form #first": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "first", "namespace": null, "ignoreCase": "quirks"}]], "#form :checkbox": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "checkbox", "data": null}]], "#form :checkbox:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "checkbox", "data": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#form :input": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "input", "data": null}]], "#form :radio": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "radio", "data": null}]], "#form :radio:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "radio", "data": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#form :radio:checked, #form :checkbox:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "radio", "data": null}, {"type": "pseudo", "name": "checked", "data": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "checkbox", "data": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#form :text": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "text", "data": null}]], "#form > #option1a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "option1a", "namespace": null, "ignoreCase": "quirks"}]], "#form > #radio1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "radio1", "namespace": null, "ignoreCase": "quirks"}]], "#form [for=action]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "for", "action": "equals", "value": "action", "namespace": null, "ignoreCase": null}]], "#form input[type='radio'], #form input[type=\"hidden\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "hidden", "namespace": null, "ignoreCase": null}]], "#form input[type='radio'], #form input[type='hidden']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "hidden", "namespace": null, "ignoreCase": null}]], "#form input[type='radio'], #form input[type=hidden]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "hidden", "namespace": null, "ignoreCase": null}]], "#form input[type=search]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "search", "namespace": null, "ignoreCase": null}]], "#form input[type=text]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "text", "namespace": null, "ignoreCase": null}]], "#form option:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#form option:not(:contains(Nothing),#option1b,:selected)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "contains", "data": "Nothing"}], [{"type": "attribute", "name": "id", "action": "equals", "value": "option1b", "namespace": null, "ignoreCase": "quirks"}], [{"type": "pseudo", "name": "selected", "data": null}]]}]], "#form option:not(:not(:selected))[id^='option3']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "selected", "data": null}]]}]]}, {"type": "attribute", "name": "id", "action": "start", "value": "option3", "namespace": null, "ignoreCase": null}]], "#form option:selected": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "selected", "data": null}]], "#form select:has(option:first-child:contains('o'))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "select", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}, {"type": "pseudo", "name": "contains", "data": "o"}]]}]], "#form select:not(.select1):contains(Nothing) > option:not(option)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "select", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "select1", "namespace": null, "ignoreCase": "quirks"}]]}, {"type": "pseudo", "name": "contains", "data": "Nothing"}, {"type": "child"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "option", "namespace": null}]]}]], "#form select:not([multiple])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "select", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "multiple", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]]}]], "#form select:not([name='select1'])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "select", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "name", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": null}]]}]], "#form select:not([name=select1])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "select", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "name", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": null}]]}]], "#grandfather > div:not(#uncle) #son": [[{"type": "attribute", "name": "id", "action": "equals", "value": "grandfather", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "equals", "value": "uncle", "namespace": null, "ignoreCase": "quirks"}]]}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "son", "namespace": null, "ignoreCase": "quirks"}]], "#groups ~ a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "groups", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "a", "namespace": null}]], "#hidden1:enabled": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hidden1", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "enabled", "data": null}]], "#hsoob": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}]], "#hsoob #spanny": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "spanny", "namespace": null, "ignoreCase": "quirks"}]], "#hsoob .a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}]], "#hsoob > div > .h": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "h", "namespace": null, "ignoreCase": "quirks"}]], "#hsoob div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "#hsoob div div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "#hsoob div.a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}]], "#hsoob span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "hsoob", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#idTest": [[{"type": "attribute", "name": "id", "action": "equals", "value": "idTest", "namespace": null, "ignoreCase": "quirks"}]], "#item_1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "item_1", "namespace": null, "ignoreCase": "quirks"}]], "#item_3": [[{"type": "attribute", "name": "id", "action": "equals", "value": "item_3", "namespace": null, "ignoreCase": "quirks"}]], "#length ~ input": [[{"type": "attribute", "name": "id", "action": "equals", "value": "length", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "input", "namespace": null}]], "#lengthtest": [[{"type": "attribute", "name": "id", "action": "equals", "value": "lengthtest", "namespace": null, "ignoreCase": "quirks"}]], "#level1 *:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#level1 *:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#level1 *:only-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "only-child", "data": null}]], "#level1 *[id$=\"_1\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "end", "value": "_1", "namespace": null, "ignoreCase": null}]], "#level1 *[id$=_1]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "end", "value": "_1", "namespace": null, "ignoreCase": null}]], "#level1 *[id*=\"2\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "any", "value": "2", "namespace": null, "ignoreCase": null}]], "#level1 *[id^=\"level2_\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "start", "value": "level2_", "namespace": null, "ignoreCase": null}]], "#level1 *[id^=level2_]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "start", "value": "level2_", "namespace": null, "ignoreCase": null}]], "#level1 > span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}]], "#level1 div:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#level1 span:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#level1:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "first-child", "data": null}]], "#level1:only-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "only-child", "data": null}]], "#level1>*:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#level1>*:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#level1>*:only-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "only-child", "data": null}]], "#level1>div:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#level1>div:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#level1>span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}]], "#level1>span:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#level2_1 + *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "universal", "namespace": null}]], "#level2_1 + span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "span", "namespace": null}]], "#level2_1 > *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}]], "#level2_1 ~ *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "universal", "namespace": null}]], "#level2_1 ~ span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "span", "namespace": null}]], "#level2_1+span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "span", "namespace": null}]], "#level2_2 + span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "span", "namespace": null}]], "#level2_2 :only-child:not(:first-child)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "only-child", "data": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "first-child", "data": null}]]}]], "#level2_2 :only-child:not(:last-child)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "only-child", "data": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "last-child", "data": null}]]}]], "#level2_2 ~ span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level2_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "span", "namespace": null}]], "#level3_1 + *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "universal", "namespace": null}]], "#level3_1 + em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "em", "namespace": null}]], "#level3_1 + span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "span", "namespace": null}]], "#level3_1 ~ #level3_2": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "id", "action": "equals", "value": "level3_2", "namespace": null, "ignoreCase": "quirks"}]], "#level3_1 ~ em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "em", "namespace": null}]], "#level3_1:empty": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "empty", "data": null}]], "#level3_2 + *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "universal", "namespace": null}]], "#level3_2 ~ *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "level3_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "universal", "namespace": null}]], "#link_2.internal": [[{"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}]], "#link_2.internal.highlight": [[{"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "highlight", "namespace": null, "ignoreCase": "quirks"}]], "#link_2.internal.nonexistent": [[{"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}]], "#list > li:nth-child(-n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "-n+2"}]], "#list > li:nth-child(n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "n+2"}]], "#list li:not(#item_1):not(#item_3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "equals", "value": "item_1", "namespace": null, "ignoreCase": "quirks"}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "equals", "value": "item_3", "namespace": null, "ignoreCase": "quirks"}]]}]], "#list>li": [[{"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}]], "#listWithTabIndex": [[{"type": "attribute", "name": "id", "action": "equals", "value": "listWithTabIndex", "namespace": null, "ignoreCase": "quirks"}]], "#liveHandlerOrder ~ div em:contains('1')": [[{"type": "attribute", "name": "id", "action": "equals", "value": "liveHandlerOrder", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "1"}]], "#lonelyBoosh": [[{"type": "attribute", "name": "id", "action": "equals", "value": "lonelyBoosh", "namespace": null, "ignoreCase": "quirks"}]], "#lonelyHsoob": [[{"type": "attribute", "name": "id", "action": "equals", "value": "<PERSON><PERSON><PERSON><PERSON>", "namespace": null, "ignoreCase": "quirks"}]], "#moretests script[src]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "moretests", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "script", "namespace": null}, {"type": "attribute", "name": "src", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#name\\+value": [[{"type": "attribute", "name": "id", "action": "equals", "value": "name+value", "namespace": null, "ignoreCase": "quirks"}]], "#nonexistent:has(*), #ap:has(*)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}], [{"type": "attribute", "name": "id", "action": "equals", "value": "ap", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]], "#oooo": [[{"type": "attribute", "name": "id", "action": "equals", "value": "oooo", "namespace": null, "ignoreCase": "quirks"}]], "#order-matters .order-matters": [[{"type": "attribute", "name": "id", "action": "equals", "value": "order-matters", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "order-matters", "namespace": null, "ignoreCase": "quirks"}]], "#p *:nth-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3"}]], "#p a:first-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "first-of-type", "data": null}]], "#p a:last-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "last-of-type", "data": null}]], "#p a:not(:first-of-type)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "first-of-type", "data": null}]]}]], "#p a:not(:last-of-type)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "last-of-type", "data": null}]]}]], "#p a:not(:nth-last-of-type(1))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "nth-last-of-type", "data": "1"}]]}]], "#p a:not(:nth-of-type(1))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "nth-of-type", "data": "1"}]]}]], "#p a:not([rel$=\"nofollow\"]) > em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]]}, {"type": "child"}, {"type": "tag", "name": "em", "namespace": null}]], "#p a:not([rel$=\"nofollow\"]) em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]]}, {"type": "descendant"}, {"type": "tag", "name": "em", "namespace": null}]], "#p a:not([rel$=\"nofollow\"])>em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]]}, {"type": "child"}, {"type": "tag", "name": "em", "namespace": null}]], "#p a:not([rel$=nofollow])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]]}]], "#p a:not([rel^=external])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "start", "value": "external", "namespace": null, "ignoreCase": null}]]}]], "#p a:not([rel~=nofollow])": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "rel", "action": "element", "value": "nofollow", "namespace": null, "ignoreCase": null}]]}]], "#p a:nth-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3"}]], "#p a:nth-last-of-type(1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-last-of-type", "data": "1"}]], "#p a:nth-of-type(1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "1"}]], "#p a:nth-of-type(2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "p", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "2"}]], "#pseudos :nth-child(+3n-2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "+3n-2"}]], "#pseudos :nth-child(-n+5)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "-n+5"}]], "#pseudos :nth-child(-n+6)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "-n+6"}]], "#pseudos :nth-child(3n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "3n"}]], "#pseudos :nth-child(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "3n+1"}]], "#pseudos :nth-child(3n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "3n+2"}]], "#pseudos :nth-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-child", "data": "odd"}]], "#pseudos :nth-last-child(-n+5)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "-n+5"}]], "#pseudos :nth-last-child(-n+6)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "-n+6"}]], "#pseudos :nth-last-child(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+1"}]], "#pseudos :nth-last-child(3n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+2"}]], "#pseudos :nth-last-child(3n-2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "3n-2"}]], "#pseudos :nth-last-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "odd"}]], "#pseudos a:first-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "first-of-type", "data": null}]], "#pseudos a:nth-last-of-type(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-last-of-type", "data": "3n+1"}]], "#pseudos a:nth-of-type(1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "1"}]], "#pseudos a:nth-of-type(3n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "3n"}]], "#pseudos a:nth-of-type(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "3n+1"}]], "#pseudos a:nth-of-type(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "odd"}]], "#pseudos a:only-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "only-of-type", "data": null}]], "#pseudos div:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#pseudos div:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#pseudos div:last-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "last-of-type", "data": null}]], "#pseudos div:nth-child(2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2"}]], "#pseudos div:nth-child(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "even"}]], "#pseudos div:nth-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "odd"}]], "#pseudos div:nth-last-child(6)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "6"}]], "#pseudos div:nth-last-child(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "even"}]], "#pseudos div:nth-last-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "odd"}]], "#pseudos div:nth-last-of-type(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-last-of-type", "data": "3n+1"}]], "#pseudos div:nth-last-of-type(5)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-last-of-type", "data": "5"}]], "#pseudos div:nth-of-type(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "nth-of-type", "data": "3n+1"}]], "#pseudos:target": [[{"type": "attribute", "name": "id", "action": "equals", "value": "pseudos", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "target", "data": null}]], "#qunit-fixture": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}]], "#qunit-fixture *[title]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "title", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#qunit-fixture :not(:has(:has(*)))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]]}]]}]], "#qunit-fixture > :nth-last-of-type(-n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "pseudo", "name": "nth-last-of-type", "data": "-n+2"}]], "#qunit-fixture > :nth-of-type(-n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "pseudo", "name": "nth-of-type", "data": "-n+2"}]], "#qunit-fixture > :only-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "pseudo", "name": "only-of-type", "data": null}]], "#qunit-fixture > p:first-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "first-of-type", "data": null}]], "#qunit-fixture > p:last-of-type": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "last-of-type", "data": null}]], "#qunit-fixture [title]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "title", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a + a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture a + a, code > a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "code", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture a +a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture a+ a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture a+a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture a:last-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "#qunit-fixture a:only-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "only-child", "data": null}]], "#qunit-fixture a[ rel = 'bookmark' ]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "equals", "value": "bookmark", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[ title ]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "title", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[TITLE]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "TITLE", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[href='http://www.google.com/']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "equals", "value": "http://www.google.com/", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[rel='bookmark']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "equals", "value": "bookmark", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[rel=bookmark]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "equals", "value": "bookmark", "namespace": null, "ignoreCase": null}]], "#qunit-fixture a[title]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "title", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#qunit-fixture div:has(div:has(div:not([id])))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]]}]]}]]}]], "#qunit-fixture div[id]:not(:has(div, span)):not(:has(*))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "div", "namespace": null}], [{"type": "tag", "name": "span", "namespace": null}]]}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]]}]], "#qunit-fixture form#form > *:nth-child(2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2"}]], "#qunit-fixture form#form > :nth-child(2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "pseudo", "name": "nth-child", "data": "2"}]], "#qunit-fixture form[id]:not([action$='formaction']):not(:button)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "action", "action": "end", "value": "formaction", "namespace": null, "ignoreCase": null}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "button", "data": null}]]}]], "#qunit-fixture form[id]:not([action='form:action']):not(:button)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "action", "action": "equals", "value": "form:action", "namespace": null, "ignoreCase": null}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "button", "data": null}]]}]], "#qunit-fixture form[id]:not([action='form:action']:button):not(:input)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "action", "action": "equals", "value": "form:action", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "button", "data": null}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "input", "data": null}]]}]], "#qunit-fixture li[tabIndex=-1]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "tabIndex", "action": "equals", "value": "-1", "namespace": null, "ignoreCase": null}]], "#qunit-fixture option[value=1]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "attribute", "name": "value", "action": "equals", "value": "1", "namespace": null, "ignoreCase": null}]], "#qunit-fixture p": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p\t": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p\n": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p\f": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p\r": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p ": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "#qunit-fixture p ~ div": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "div", "namespace": null}]], "#qunit-fixture p, #qunit-fixture p a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}]], "#qunit-fixture p:FIRST-CHILD": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#qunit-fixture p:first-child": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "#qunit-fixture p:has(:contains(mark)):has(code)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "pseudo", "name": "contains", "data": "mark"}]]}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "code", "namespace": null}]]}]], "#qunit-fixture p:has(:contains(mark)):has(code):contains(This link)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "pseudo", "name": "contains", "data": "mark"}]]}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "code", "namespace": null}]]}, {"type": "pseudo", "name": "contains", "data": "This link"}]], "#qunit-fixture p:not( a )": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}]], "#qunit-fixture p:not( p )": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "p", "namespace": null}]]}]], "#qunit-fixture p:not(#blargh)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:not(.foo)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:not(:has(a), :nth-child(1))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}], [{"type": "pseudo", "name": "nth-child", "data": "1"}]]}]], "#qunit-fixture p:not(:nth-child(1))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "nth-child", "data": "1"}]]}]], "#qunit-fixture p:not(:nth-last-child(1))": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "nth-last-child", "data": "1"}]]}]], "#qunit-fixture p:not(a)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}]], "#qunit-fixture p:not(a, b)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}]]}]], "#qunit-fixture p:not(a, b, div)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}], [{"type": "tag", "name": "div", "namespace": null}]]}]], "#qunit-fixture p:not(div#blargh)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:not(div.foo)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:not(p#blargh)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:not(p.foo)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]]}]], "#qunit-fixture p:parent": [[{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "parent", "data": null}]], "#seite1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "seite1", "namespace": null, "ignoreCase": "quirks"}]], "#select1 *:nth-last-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3"}]], "#select1 :nth-last-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "nth-last-child", "data": "3"}]], "#select1 option:NTH-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3"}]], "#select1 option:NTH-last-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3"}]], "#select1 option:nth-child(+2n + 1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "+2n + 1"}]], "#select1 option:nth-child(-1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "-1"}]], "#select1 option:nth-child(-1n + 3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "-1n + 3"}]], "#select1 option:nth-child(-1n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "-1n+3"}]], "#select1 option:nth-child(-n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "-n+3"}]], "#select1 option:nth-child(1n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "1n"}]], "#select1 option:nth-child(1n+0)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "1n+0"}]], "#select1 option:nth-child(2n + 1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2n + 1"}]], "#select1 option:nth-child(2n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2n"}]], "#select1 option:nth-child(2n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2n+1"}]], "#select1 option:nth-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3"}]], "#select1 option:nth-child(3n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n"}]], "#select1 option:nth-child(3n+0)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n+0"}]], "#select1 option:nth-child(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n+1"}]], "#select1 option:nth-child(3n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n+2"}]], "#select1 option:nth-child(3n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n+3"}]], "#select1 option:nth-child(3n-1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n-1"}]], "#select1 option:nth-child(3n-2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n-2"}]], "#select1 option:nth-child(3n-3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "3n-3"}]], "#select1 option:nth-child(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "even"}]], "#select1 option:nth-child(n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "n"}]], "#select1 option:nth-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "odd"}]], "#select1 option:nth-last-child(+2n + 1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "+2n + 1"}]], "#select1 option:nth-last-child(-1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "-1"}]], "#select1 option:nth-last-child(-1n + 3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "-1n + 3"}]], "#select1 option:nth-last-child(-1n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "-1n+3"}]], "#select1 option:nth-last-child(-n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "-n+3"}]], "#select1 option:nth-last-child(1n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "1n"}]], "#select1 option:nth-last-child(1n+0)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "1n+0"}]], "#select1 option:nth-last-child(2n + 1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "2n + 1"}]], "#select1 option:nth-last-child(2n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "2n"}]], "#select1 option:nth-last-child(2n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "2n+1"}]], "#select1 option:nth-last-child(3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3"}]], "#select1 option:nth-last-child(3n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n"}]], "#select1 option:nth-last-child(3n+0)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+0"}]], "#select1 option:nth-last-child(3n+1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+1"}]], "#select1 option:nth-last-child(3n+2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+2"}]], "#select1 option:nth-last-child(3n+3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n+3"}]], "#select1 option:nth-last-child(3n-1)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n-1"}]], "#select1 option:nth-last-child(3n-2)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n-2"}]], "#select1 option:nth-last-child(3n-3)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "3n-3"}]], "#select1 option:nth-last-child(even)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "even"}]], "#select1 option:nth-last-child(n)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "n"}]], "#select1 option:nth-last-child(odd)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "odd"}]], "#select1 option:selected": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "selected", "data": null}]], "#select1 option[value!='']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "attribute", "name": "value", "action": "not", "value": "", "namespace": null, "ignoreCase": null}]], "#select1 option[value='']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "attribute", "name": "value", "action": "equals", "value": "", "namespace": null, "ignoreCase": null}]], "#select2 option:selected": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "selected", "data": null}]], "#select2 option[selected='selected']": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "attribute", "name": "selected", "action": "equals", "value": "selected", "namespace": null, "ignoreCase": null}]], "#select2 option[selected]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "attribute", "name": "selected", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#select3 option:selected": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select3", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "selected", "data": null}]], "#sep": [[{"type": "attribute", "name": "id", "action": "equals", "value": "sep", "namespace": null, "ignoreCase": "quirks"}]], "#sibling-selector + .sibling-selector": [[{"type": "attribute", "name": "id", "action": "equals", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}]], "#sibling-selector + div.sibling-selector": [[{"type": "attribute", "name": "id", "action": "equals", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}]], "#sibling-selector ~ .sibling-selector": [[{"type": "attribute", "name": "id", "action": "equals", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}]], "#sibling-selector ~ div.sibling-selector": [[{"type": "attribute", "name": "id", "action": "equals", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling-selector", "namespace": null, "ignoreCase": "quirks"}]], "#siblingTest > em *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "descendant"}, {"type": "universal", "namespace": null}]], "#siblingTest > em:contains('x') + em ~ span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "x"}, {"type": "adjacent"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "span", "namespace": null}]], "#siblingTest > em:first-child + em ~ span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}, {"type": "adjacent"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "span", "namespace": null}]], "#siblingTest em *": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "descendant"}, {"type": "universal", "namespace": null}]], "#siblingTest em ~ em ~ em ~ span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "span", "namespace": null}]], "#siblingfirst ~ em": [[{"type": "attribute", "name": "id", "action": "equals", "value": "siblingfirst", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "em", "namespace": null}]], "#spaced-tokens    p    em    a": [[{"type": "attribute", "name": "id", "action": "equals", "value": "spaced-tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "em", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}]], "#spanny": [[{"type": "attribute", "name": "id", "action": "equals", "value": "spanny", "namespace": null, "ignoreCase": "quirks"}]], "#tName1": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName1", "namespace": null, "ignoreCase": "quirks"}]], "#tName1 span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName1", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#tName1-span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName1-span", "namespace": null, "ignoreCase": "quirks"}]], "#tName2": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName2", "namespace": null, "ignoreCase": "quirks"}]], "#tName2 span": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "#tName2ID": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tName2ID", "namespace": null, "ignoreCase": "quirks"}]], "#test\\.foo\\[5\\]bar": [[{"type": "attribute", "name": "id", "action": "equals", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], "#tmp_input :button": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tmp_input", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "button", "data": null}]], "#tmp_input :reset": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tmp_input", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "reset", "data": null}]], "#tmp_input :submit": [[{"type": "attribute", "name": "id", "action": "equals", "value": "tmp_input", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "pseudo", "name": "submit", "data": null}]], "#token-four": [[{"type": "attribute", "name": "id", "action": "equals", "value": "token-four", "namespace": null, "ignoreCase": "quirks"}]], "#troubleForm": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}]], "#troubleForm *:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#troubleForm *[type=radio]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}]], "#troubleForm *[type]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "attribute", "name": "type", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#troubleForm > p > *:disabled": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "disabled", "data": null}]], "#troubleForm [type=radio]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}]], "#troubleForm [type]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "type", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "#troubleForm2 input[name=\"brackets[5][]\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "brackets[5][]", "namespace": null, "ignoreCase": null}]], "#troubleForm2 input[name=\"brackets[5][]\"]:checked": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "brackets[5][]", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "checked", "data": null}]], "#troubleForm2 input[name=\"brackets[5][]\"][value=\"2\"]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "troubleForm2", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "brackets[5][]", "namespace": null, "ignoreCase": null}, {"type": "attribute", "name": "value", "action": "equals", "value": "2", "namespace": null, "ignoreCase": null}]], "#types_all": [[{"type": "attribute", "name": "id", "action": "equals", "value": "types_all", "namespace": null, "ignoreCase": "quirks"}]], "#uncle": [[{"type": "attribute", "name": "id", "action": "equals", "value": "uncle", "namespace": null, "ignoreCase": "quirks"}]], "#台北Táiběi": [[{"type": "attribute", "name": "id", "action": "equals", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}]], "#台北Táiběi, #台北": [[{"type": "attribute", "name": "id", "action": "equals", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}], [{"type": "attribute", "name": "id", "action": "equals", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], "*": [[{"type": "universal", "namespace": null}]], "* :not(*) foo": [[{"type": "universal", "namespace": null}, {"type": "descendant"}, {"type": "pseudo", "name": "not", "data": [[{"type": "universal", "namespace": null}]]}, {"type": "descendant"}, {"type": "tag", "name": "foo", "namespace": null}]], "* < *": [[{"type": "universal", "namespace": null}, {"type": "parent"}, {"type": "universal", "namespace": null}]], "*, foo": [[{"type": "universal", "namespace": null}], [{"type": "tag", "name": "foo", "namespace": null}]], "*,:contains(!)": [[{"type": "universal", "namespace": null}], [{"type": "pseudo", "name": "contains", "data": "!"}]], "*:contains(humans)": [[{"type": "universal", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "humans"}]], "*[id]": [[{"type": "universal", "namespace": null}, {"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "*[name=iframe]": [[{"type": "universal", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "iframe", "namespace": null, "ignoreCase": null}]], "*[type=checkbox]": [[{"type": "universal", "namespace": null}, {"type": "attribute", "name": "type", "action": "equals", "value": "checkbox", "namespace": null, "ignoreCase": null}]], ".GROUPS": [[{"type": "attribute", "name": "class", "action": "element", "value": "GROUPS", "namespace": null, "ignoreCase": "quirks"}]], ".a": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}]], ".a #booshTest #spanny": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "spanny", "namespace": null, "ignoreCase": "quirks"}]], ".a #spanny": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "spanny", "namespace": null, "ignoreCase": "quirks"}]], ".a .d + .sib": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "d", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "attribute", "name": "class", "action": "element", "value": "sib", "namespace": null, "ignoreCase": "quirks"}]], ".a .d ~ .sib[test=\"f g\"]": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "d", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sib", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "test", "action": "equals", "value": "f g", "namespace": null, "ignoreCase": null}]], ".a > #booshTest": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], ".a span": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], ".a.b #booshTest": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "b", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], ".a>#booshTest": [[{"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], ".blog": [[{"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}]], ".blog.link": [[{"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "link", "namespace": null, "ignoreCase": "quirks"}]], ".brothers": [[{"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]], ".class-with-dashes": [[{"type": "attribute", "name": "class", "action": "element", "value": "class-with-dashes", "namespace": null, "ignoreCase": "quirks"}]], ".component": [[{"type": "attribute", "name": "class", "action": "element", "value": "component", "namespace": null, "ignoreCase": "quirks"}]], ".container div:not(.excluded) div": [[{"type": "attribute", "name": "class", "action": "element", "value": "container", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "excluded", "namespace": null, "ignoreCase": "quirks"}]]}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], ".d #oooo #emem": [[{"type": "attribute", "name": "class", "action": "element", "value": "d", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "oooo", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "emem", "namespace": null, "ignoreCase": "quirks"}]], ".d ~ .sib": [[{"type": "attribute", "name": "class", "action": "element", "value": "d", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sib", "namespace": null, "ignoreCase": "quirks"}]], ".d.i #emem": [[{"type": "attribute", "name": "class", "action": "element", "value": "d", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "i", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "emem", "namespace": null, "ignoreCase": "quirks"}]], ".direct-descend > .direct-descend .lvl2": [[{"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "lvl2", "namespace": null, "ignoreCase": "quirks"}]], ".direct-descend > .direct-descend > .direct-descend ~ .lvl2": [[{"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "lvl2", "namespace": null, "ignoreCase": "quirks"}]], ".direct-descend > .direct-descend div": [[{"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "direct-descend", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], ".e": [[{"type": "attribute", "name": "class", "action": "element", "value": "e", "namespace": null, "ignoreCase": "quirks"}]], ".e.hasOwnProperty.toString": [[{"type": "attribute", "name": "class", "action": "element", "value": "e", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "hasOwnProperty", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "toString", "namespace": null, "ignoreCase": "quirks"}]], ".excluded": [[{"type": "attribute", "name": "class", "action": "element", "value": "excluded", "namespace": null, "ignoreCase": "quirks"}]], ".first": [[{"type": "attribute", "name": "class", "action": "element", "value": "first", "namespace": null, "ignoreCase": "quirks"}]], ".foo": [[{"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]], ".foo\\:bar": [[{"type": "attribute", "name": "class", "action": "element", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], ".fototab > .thumbnails > a": [[{"type": "attribute", "name": "class", "action": "element", "value": "fototab", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "thumbnails", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], ".internal#link_2": [[{"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}]], ".link": [[{"type": "attribute", "name": "class", "action": "element", "value": "link", "namespace": null, "ignoreCase": "quirks"}]], ".nothiddendiv div:first-child": [[{"type": "attribute", "name": "class", "action": "element", "value": "nothiddendiv", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], ".null": [[{"type": "attribute", "name": "class", "action": "element", "value": "null", "namespace": null, "ignoreCase": "quirks"}]], ".null div": [[{"type": "attribute", "name": "class", "action": "element", "value": "null", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], ".odd:not(div)": [[{"type": "attribute", "name": "class", "action": "element", "value": "odd", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "div", "namespace": null}]]}]], ".parent .middle + .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".parent .middle + h2": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "h2", "namespace": null}]], ".parent .middle + h3": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "h3", "namespace": null}]], ".parent .middle + h4": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "h4", "namespace": null}]], ".parent .middle ~ .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".parent .middle ~ h2": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "h2", "namespace": null}]], ".parent .middle ~ h3": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "h3", "namespace": null}]], ".parent .middle ~ h4": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "h4", "namespace": null}]], ".parent .middle ~ h4.younger": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "middle", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "tag", "name": "h4", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "younger", "namespace": null, "ignoreCase": "quirks"}]], ".parent .oldest + .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "oldest", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".parent .oldest ~ .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "oldest", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".parent .youngest + .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "youngest", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".parent .youngest ~ .sibling": [[{"type": "attribute", "name": "class", "action": "element", "value": "parent", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "youngest", "namespace": null, "ignoreCase": "quirks"}, {"type": "sibling"}, {"type": "attribute", "name": "class", "action": "element", "value": "sibling", "namespace": null, "ignoreCase": "quirks"}]], ".second": [[{"type": "attribute", "name": "class", "action": "element", "value": "second", "namespace": null, "ignoreCase": "quirks"}]], ".select1": [[{"type": "attribute", "name": "class", "action": "element", "value": "select1", "namespace": null, "ignoreCase": "quirks"}]], ".test\\.foo\\[5\\]bar": [[{"type": "attribute", "name": "class", "action": "element", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], ".台北": [[{"type": "attribute", "name": "class", "action": "element", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], ".台北Táiběi": [[{"type": "attribute", "name": "class", "action": "element", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}]], ".台北Táiběi, .台北": [[{"type": "attribute", "name": "class", "action": "element", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}], [{"type": "attribute", "name": "class", "action": "element", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], ".台北Táiběi.台北": [[{"type": "attribute", "name": "class", "action": "element", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], ":Header": [[{"type": "pseudo", "name": "header", "data": null}]], ":button": [[{"type": "pseudo", "name": "button", "data": null}]], ":contains(Nothing),#option1b,:selected": [[{"type": "pseudo", "name": "contains", "data": "Nothing"}], [{"type": "attribute", "name": "id", "action": "equals", "value": "option1b", "namespace": null, "ignoreCase": "quirks"}], [{"type": "pseudo", "name": "selected", "data": null}]], ":contains(foo)": [[{"type": "pseudo", "name": "contains", "data": "foo"}]], ":contains(humans)": [[{"type": "pseudo", "name": "contains", "data": "humans"}]], ":contains(mark)": [[{"type": "pseudo", "name": "contains", "data": "mark"}]], ":empty": [[{"type": "pseudo", "name": "empty", "data": null}]], ":first-child": [[{"type": "pseudo", "name": "first-child", "data": null}]], ":first-child(n)": [[{"type": "pseudo", "name": "first-child", "data": "n"}]], ":first-last-child": [[{"type": "pseudo", "name": "first-last-child", "data": null}]], ":first-of-type": [[{"type": "pseudo", "name": "first-of-type", "data": null}]], ":has(*)": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]], ":has(*,:contains(!)),:contains(!)": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}], [{"type": "pseudo", "name": "contains", "data": "!"}]]}], [{"type": "pseudo", "name": "contains", "data": "!"}]], ":has(:has(*))": [[{"type": "pseudo", "name": "has", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]]}]], ":has(:nth-child(-1n-1))": [[{"type": "pseudo", "name": "has", "data": [[{"type": "pseudo", "name": "nth-child", "data": "-1n-1"}]]}]], ":has(a),:nth-child(1)": [[{"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}], [{"type": "pseudo", "name": "nth-child", "data": "1"}]], ":has(div,span)": [[{"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "div", "namespace": null}], [{"type": "tag", "name": "span", "namespace": null}]]}]], ":has(option)": [[{"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "option", "namespace": null}]]}]], ":header": [[{"type": "pseudo", "name": "header", "data": null}]], ":humanoid": [[{"type": "pseudo", "name": "humanoid", "data": null}]], ":image,:input,:submit": [[{"type": "pseudo", "name": "image", "data": null}], [{"type": "pseudo", "name": "input", "data": null}], [{"type": "pseudo", "name": "submit", "data": null}]], ":input": [[{"type": "pseudo", "name": "input", "data": null}]], ":input:not(:image,:input,:submit)": [[{"type": "pseudo", "name": "input", "data": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "image", "data": null}], [{"type": "pseudo", "name": "input", "data": null}], [{"type": "pseudo", "name": "submit", "data": null}]]}]], ":input[data-pos=':first']": [[{"type": "pseudo", "name": "input", "data": null}, {"type": "attribute", "name": "data-pos", "action": "equals", "value": ":first", "namespace": null, "ignoreCase": null}]], ":last-child": [[{"type": "pseudo", "name": "last-child", "data": null}]], ":last-child(n)": [[{"type": "pseudo", "name": "last-child", "data": "n"}]], ":last-last-child": [[{"type": "pseudo", "name": "last-last-child", "data": null}]], ":last-of-type": [[{"type": "pseudo", "name": "last-of-type", "data": null}]], ":not(*)": [[{"type": "pseudo", "name": "not", "data": [[{"type": "universal", "namespace": null}]]}]], ":not(:not(*))": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "not", "data": [[{"type": "universal", "namespace": null}]]}]]}]], ":not(:not(:not(*)))": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "not", "data": [[{"type": "universal", "namespace": null}]]}]]}]]}]], ":not(:nth-child(-1n-1))": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "nth-child", "data": "-1n-1"}]]}]], ":not(:selected)": [[{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "selected", "data": null}]]}]], ":not(code)": [[{"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "code", "namespace": null}]]}]], ":nth-child": [[{"type": "pseudo", "name": "nth-child", "data": null}]], ":nth-child(- 1n)": [[{"type": "pseudo", "name": "nth-child", "data": "- 1n"}]], ":nth-child(-)": [[{"type": "pseudo", "name": "nth-child", "data": "-"}]], ":nth-child(-1 n)": [[{"type": "pseudo", "name": "nth-child", "data": "-1 n"}]], ":nth-child(-1n-1)": [[{"type": "pseudo", "name": "nth-child", "data": "-1n-1"}]], ":nth-child(1)": [[{"type": "pseudo", "name": "nth-child", "data": "1"}]], ":nth-child(2+0)": [[{"type": "pseudo", "name": "nth-child", "data": "2+0"}]], ":nth-child(2n+-0)": [[{"type": "pseudo", "name": "nth-child", "data": "2n+-0"}]], ":nth-child(asdf)": [[{"type": "pseudo", "name": "nth-child", "data": "asdf"}]], ":nth-last-child(1)": [[{"type": "pseudo", "name": "nth-last-child", "data": "1"}]], ":nth-last-last-child(1)": [[{"type": "pseudo", "name": "nth-last-last-child", "data": "1"}]], ":nth-last-of-type(-1)": [[{"type": "pseudo", "name": "nth-last-of-type", "data": "-1"}]], ":nth-last-of-type(1)": [[{"type": "pseudo", "name": "nth-last-of-type", "data": "1"}]], ":nth-of-type(-1)": [[{"type": "pseudo", "name": "nth-of-type", "data": "-1"}]], ":nth-of-type(1)": [[{"type": "pseudo", "name": "nth-of-type", "data": "1"}]], ":only-child(n)": [[{"type": "pseudo", "name": "only-child", "data": "n"}]], ":only-last-child": [[{"type": "pseudo", "name": "only-last-child", "data": null}]], ":parent": [[{"type": "pseudo", "name": "parent", "data": null}]], ":reset": [[{"type": "pseudo", "name": "reset", "data": null}]], ":root": [[{"type": "pseudo", "name": "root", "data": null}]], ":selected": [[{"type": "pseudo", "name": "selected", "data": null}]], ":submit": [[{"type": "pseudo", "name": "submit", "data": null}]], ":visble": [[{"type": "pseudo", "name": "visble", "data": null}]], ">.a>#booshTest": [[{"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "a", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "booshTest", "namespace": null, "ignoreCase": "quirks"}]], "[action$='formaction']": [[{"type": "attribute", "name": "action", "action": "end", "value": "formaction", "namespace": null, "ignoreCase": null}]], "[action='form:action']": [[{"type": "attribute", "name": "action", "action": "equals", "value": "form:action", "namespace": null, "ignoreCase": null}]], "[action='form:action']:button": [[{"type": "attribute", "name": "action", "action": "equals", "value": "form:action", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "button", "data": null}]], "[attr=boosh]": [[{"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "[attr=foo]": [[{"type": "attribute", "name": "attr", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": null}]], "[attr]": [[{"type": "attribute", "name": "attr", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[class*=component]": [[{"type": "attribute", "name": "class", "action": "any", "value": "component", "namespace": null, "ignoreCase": null}]], "[class~=brothers]": [[{"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": null}]], "[class~=internal]": [[{"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": null}]], "[constructor='foo']": [[{"type": "attribute", "name": "constructor", "action": "equals", "value": "foo", "namespace": null, "ignoreCase": null}]], "[constructor]": [[{"type": "attribute", "name": "constructor", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[foo]": [[{"type": "attribute", "name": "foo", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[foo^=\"bar\"]": [[{"type": "attribute", "name": "foo", "action": "start", "value": "bar", "namespace": null, "ignoreCase": null}]], "[href=\"#\"]": [[{"type": "attribute", "name": "href", "action": "equals", "value": "#", "namespace": null, "ignoreCase": null}]], "[href]": [[{"type": "attribute", "name": "href", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[ a=b i ]": [[{"type": "attribute", "name": "a", "action": "equals", "value": "b", "namespace": null, "ignoreCase": true}]], "[id*=option1]": [[{"type": "attribute", "name": "id", "action": "any", "value": "option1", "namespace": null, "ignoreCase": null}]], "[id*=option1][type!=checkbox]": [[{"type": "attribute", "name": "id", "action": "any", "value": "option1", "namespace": null, "ignoreCase": null}, {"type": "attribute", "name": "type", "action": "not", "value": "checkbox", "namespace": null, "ignoreCase": null}]], "[id='select1'] *:not(:last-child), [id='select2'] *:not(:last-child)": [[{"type": "attribute", "name": "id", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "last-child", "data": null}]]}], [{"type": "attribute", "name": "id", "action": "equals", "value": "select2", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "universal", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "last-child", "data": null}]]}]], "[id=option1a]": [[{"type": "attribute", "name": "id", "action": "equals", "value": "option1a", "namespace": null, "ignoreCase": null}]], "[id]": [[{"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[multiple]": [[{"type": "attribute", "name": "multiple", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[name='id']": [[{"type": "attribute", "name": "name", "action": "equals", "value": "id", "namespace": null, "ignoreCase": null}]], "[name='select1']": [[{"type": "attribute", "name": "name", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": null}]], "[name=div]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "div", "namespace": null, "ignoreCase": null}]], "[name=example]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "example", "namespace": null, "ignoreCase": null}]], "[name=prop2]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "prop2", "namespace": null, "ignoreCase": null}]], "[name=select1]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "select1", "namespace": null, "ignoreCase": null}]], "[name=tName1]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "tName1", "namespace": null, "ignoreCase": null}]], "[name=tName2]": [[{"type": "attribute", "name": "name", "action": "equals", "value": "tName2", "namespace": null, "ignoreCase": null}]], "[rel$=\"nofollow\"]": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]], "[rel$=nofollow]": [[{"type": "attribute", "name": "rel", "action": "end", "value": "nofollow", "namespace": null, "ignoreCase": null}]], "[rel^=external]": [[{"type": "attribute", "name": "rel", "action": "start", "value": "external", "namespace": null, "ignoreCase": null}]], "[rel~=nofollow]": [[{"type": "attribute", "name": "rel", "action": "element", "value": "nofollow", "namespace": null, "ignoreCase": null}]], "[test=]": [[{"type": "attribute", "name": "test", "action": "equals", "value": "", "namespace": null, "ignoreCase": null}]], "[test^='']": [[{"type": "attribute", "name": "test", "action": "start", "value": "", "namespace": null, "ignoreCase": null}]], "[title]": [[{"type": "attribute", "name": "title", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[type=checkbox]": [[{"type": "attribute", "name": "type", "action": "equals", "value": "checkbox", "namespace": null, "ignoreCase": null}]], "[type=radio]": [[{"type": "attribute", "name": "type", "action": "equals", "value": "radio", "namespace": null, "ignoreCase": null}]], "[watch='bar']": [[{"type": "attribute", "name": "watch", "action": "equals", "value": "bar", "namespace": null, "ignoreCase": null}]], "[watch]": [[{"type": "attribute", "name": "watch", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "[xml\\:test]": [[{"type": "attribute", "name": "xml:test", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "a": [[{"type": "tag", "name": "a", "namespace": null}]], "a#link_2.internal": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}]], "a,b": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}]], "a,b,div": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}], [{"type": "tag", "name": "div", "namespace": null}]], "a,p": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "p", "namespace": null}]], "a,p,b": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "p", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}]], "a.GROUPS + code + a": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "GROUPS", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "code", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "a", "namespace": null}]], "a.blog": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}]], "a.blog:not(.link)": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "link", "namespace": null, "ignoreCase": "quirks"}]]}]], "a.highlight.internal": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "highlight", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}]], "a.highlight.internal.nonexistent": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "highlight", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}]], "a.internal": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}]], "a.internal#link_2": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "id", "action": "equals", "value": "link_2", "namespace": null, "ignoreCase": "quirks"}]], "a.internal.highlight": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "highlight", "namespace": null, "ignoreCase": "quirks"}]], "a.odd": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "odd", "namespace": null, "ignoreCase": "quirks"}]], "a:contains(\"(Link)\")": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "(Link)"}]], "a:contains('')": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": ""}]], "a:contains('Google Groups (Link)')": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "Google Groups (Link)"}]], "a:contains((Link))": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "(Link)"}]], "a:contains(Google Groups (Link))": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "Google Groups (Link)"}]], "a:contains(Google Groups)": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "Google Groups"}]], "a:contains(Google)": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "Google"}]], "a:not([href=\"#\"])": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "href", "action": "equals", "value": "#", "namespace": null, "ignoreCase": null}]]}]], "a[class*=blog]:not(:has(*, :contains(!)), :contains(!)), br:contains(]), p:contains(]), :not(:empty):not(:parent)": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "any", "value": "blog", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}], [{"type": "pseudo", "name": "contains", "data": "!"}]]}], [{"type": "pseudo", "name": "contains", "data": "!"}]]}], [{"type": "tag", "name": "br", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "]"}], [{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "]"}], [{"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "empty", "data": null}]]}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "parent", "data": null}]]}]], "a[class~=\"internal\"]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": null}]], "a[class~=external]:not([href=\"#\"])": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "external", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "href", "action": "equals", "value": "#", "namespace": null, "ignoreCase": null}]]}]], "a[class~=external][href=\"#\"]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "external", "namespace": null, "ignoreCase": null}, {"type": "attribute", "name": "href", "action": "equals", "value": "#", "namespace": null, "ignoreCase": null}]], "a[class~=internal]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "internal", "namespace": null, "ignoreCase": null}]], "a[href $= 'org/']": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "end", "value": "org/", "namespace": null, "ignoreCase": null}]], "a[href *= 'google']": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "any", "value": "google", "namespace": null, "ignoreCase": null}]], "a[href ^= 'http://www']": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "start", "value": "http://www", "namespace": null, "ignoreCase": null}]], "a[href*=#]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "any", "value": "#", "namespace": null, "ignoreCase": null}]], "a[href=\"#\"]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "equals", "value": "#", "namespace": null, "ignoreCase": null}]], "a[href]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "a[rel^=\"external\"]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "start", "value": "external", "namespace": null, "ignoreCase": null}]], "a[rel^='external']": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "start", "value": "external", "namespace": null, "ignoreCase": null}]], "a[rel^=external]": [[{"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "rel", "action": "start", "value": "external", "namespace": null, "ignoreCase": null}]], "body": [[{"type": "tag", "name": "body", "namespace": null}]], "body div div div": [[{"type": "tag", "name": "body", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "body#body": [[{"type": "tag", "name": "body", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "body", "namespace": null, "ignoreCase": "quirks"}]], "body>div div div": [[{"type": "tag", "name": "body", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "cite[title=\"hello world!\"]": [[{"type": "tag", "name": "cite", "namespace": null}, {"type": "attribute", "name": "title", "action": "equals", "value": "hello world!", "namespace": null, "ignoreCase": null}]], "code": [[{"type": "tag", "name": "code", "namespace": null}]], "code > *": [[{"type": "tag", "name": "code", "namespace": null}, {"type": "child"}, {"type": "universal", "namespace": null}]], "component": [[{"type": "tag", "name": "component", "namespace": null}]], "component#seite1": [[{"type": "tag", "name": "component", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "seite1", "namespace": null, "ignoreCase": "quirks"}]], "div": [[{"type": "tag", "name": "div", "namespace": null}]], "div #foo\\:bar": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], "div #test\\.foo\\[5\\]bar": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], "div #台北": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], "div .foo\\:bar": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], "div .test\\.foo\\[5\\]bar": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], "div .tokens[title=\"one two three #%\"]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "one two three #%", "namespace": null, "ignoreCase": null}]], "div .tokens[title=\"one two three #%\"] a[href$=foo] div": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "one two three #%", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "end", "value": "foo", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "div .tokens[title=\"one two\"]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "one two", "namespace": null, "ignoreCase": null}]], "div .tokens[title=\"one\"]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "one", "namespace": null, "ignoreCase": null}]], "div .tokens[title='one two three #%'] a": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "tokens", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "one two three #%", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}]], "div .台北Táiběi": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}]], "div > #nonexistent": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}]], "div > div #tName1": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "id", "action": "equals", "value": "tName1", "namespace": null, "ignoreCase": "quirks"}]], "div > span": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}]], "div ~ #level2_3": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "sibling"}, {"type": "attribute", "name": "id", "action": "equals", "value": "level2_3", "namespace": null, "ignoreCase": "quirks"}]], "div ~ #level3_2": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "sibling"}, {"type": "attribute", "name": "id", "action": "equals", "value": "level3_2", "namespace": null, "ignoreCase": "quirks"}]], "div#attr-child-boosh[attr=boosh]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "div#attr-test3.found.you[title=\"whatup duders\"]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-test3", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "found", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "you", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "title", "action": "equals", "value": "whatup duders", "namespace": null, "ignoreCase": null}]], "div#blargh": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]], "div#fixtures > div a": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}]], "div#fixtures div ~ a div": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "div", "namespace": null}]], "div#fixtures p": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "div#fixtures>div a": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "fixtures", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}]], "div#form": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "form", "namespace": null, "ignoreCase": "quirks"}]], "div#grandfather > div": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "grandfather", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "div", "namespace": null}]], "div,span": [[{"type": "tag", "name": "div", "namespace": null}], [{"type": "tag", "name": "span", "namespace": null}]], "div.blah > p > a": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "blah", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "div.brothers": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]], "div.brothers + div": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "div", "namespace": null}]], "div.brothers + div.brothers": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]], "div.brothers:not(.brothers)": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]]}]], "div.foo": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]], "div.foo > span > a": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "div:has(div:not([id]))": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]]}]]}]], "div:not(.brothers)": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]]}]], "div:not([class~=brothers])": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": null}]]}]], "div:not([id])": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "attribute", "name": "id", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]]}]], "div[class$=men]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "end", "value": "men", "namespace": null, "ignoreCase": null}]], "div[class*=\"ers m\"]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "any", "value": "ers m", "namespace": null, "ignoreCase": null}]], "div[class^=bro]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "start", "value": "bro", "namespace": null, "ignoreCase": null}]], "div[class~=brothers]": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": null}]], "div[class~=brothers].brothers": [[{"type": "tag", "name": "div", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": null}, {"type": "attribute", "name": "class", "action": "element", "value": "brothers", "namespace": null, "ignoreCase": "quirks"}]], "dl\tol": [[{"type": "tag", "name": "dl", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "ol", "namespace": null}]], "dl ol": [[{"type": "tag", "name": "dl", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "ol", "namespace": null}]], "elem:not(:has(*))": [[{"type": "tag", "name": "elem", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "has", "data": [[{"type": "universal", "namespace": null}]]}]]}]], "em[nopass~=\"copyright\"]": [[{"type": "tag", "name": "em", "namespace": null}, {"type": "attribute", "name": "nopass", "action": "element", "value": "copyright", "namespace": null, "ignoreCase": null}]], "em[rel~=\"copyright\"]": [[{"type": "tag", "name": "em", "namespace": null}, {"type": "attribute", "name": "rel", "action": "element", "value": "copyright", "namespace": null, "ignoreCase": null}]], "foo_bar": [[{"type": "tag", "name": "foo_bar", "namespace": null}]], "form": [[{"type": "tag", "name": "form", "namespace": null}]], "form > #foo\\:bar": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], "form > #test\\.foo\\[5\\]bar": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], "form > #台北": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "id", "action": "equals", "value": "台北", "namespace": null, "ignoreCase": "quirks"}]], "form > .foo\\:bar": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "foo:bar", "namespace": null, "ignoreCase": "quirks"}]], "form > .test\\.foo\\[5\\]bar": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "test.foo[5]bar", "namespace": null, "ignoreCase": "quirks"}]], "form > .台北Táiběi": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "child"}, {"type": "attribute", "name": "class", "action": "element", "value": "台北Táiběi", "namespace": null, "ignoreCase": "quirks"}]], "form label[for]": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "label", "namespace": null}, {"type": "attribute", "name": "for", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "form:nth-last-child( 5 )": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": " 5 "}]], "form:nth-last-child(5)": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "5"}]], "form[title*=\"commas,\"], input[value=\"#commaOne,#commaTwo\"]": [[{"type": "tag", "name": "form", "namespace": null}, {"type": "attribute", "name": "title", "action": "any", "value": "commas,", "namespace": null, "ignoreCase": null}], [{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "value", "action": "equals", "value": "#commaOne,#commaTwo", "namespace": null, "ignoreCase": null}]], "h1": [[{"type": "tag", "name": "h1", "namespace": null}]], "h1 ~ ul": [[{"type": "tag", "name": "h1", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "ul", "namespace": null}]], "h1[CLASS]": [[{"type": "tag", "name": "h1", "namespace": null}, {"type": "attribute", "name": "CLASS", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "h1[class]": [[{"type": "tag", "name": "h1", "namespace": null}, {"type": "attribute", "name": "class", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "h2\t,\r#qunit-fixture p\n": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "h2 , #qunit-fixture p": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "h2, #qunit-fixture p": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "h2, h1": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "tag", "name": "h1", "namespace": null}]], "h2,#qunit-fixture p": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "h2,#qunit-fixture p ": [[{"type": "tag", "name": "h2", "namespace": null}], [{"type": "attribute", "name": "id", "action": "equals", "value": "qunit-fixture", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "html": [[{"type": "tag", "name": "html", "namespace": null}]], "input": [[{"type": "tag", "name": "input", "namespace": null}]], "input[data-attr='\\01D306A']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "data-attr", "action": "equals", "value": "𝌆A", "namespace": null, "ignoreCase": null}]], "input[data-comma=\"0,1\"]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "data-comma", "action": "equals", "value": "0,1", "namespace": null, "ignoreCase": null}]], "input[data-comma='0,1']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "data-comma", "action": "equals", "value": "0,1", "namespace": null, "ignoreCase": null}]], "input[data-pos=':first']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "data-pos", "action": "equals", "value": ":first", "namespace": null, "ignoreCase": null}]], "input[data-pos=\\:first]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "data-pos", "action": "equals", "value": ":first", "namespace": null, "ignoreCase": null}]], "input[id='idTest']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "idTest", "namespace": null, "ignoreCase": null}]], "input[id=types_all]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "types_all", "namespace": null, "ignoreCase": null}]], "input[name$='[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "end", "value": "[bar]", "namespace": null, "ignoreCase": null}]], "input[name$='bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "end", "value": "bar]", "namespace": null, "ignoreCase": null}]], "input[name$='foo[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "end", "value": "foo[bar]", "namespace": null, "ignoreCase": null}]], "input[name*='[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "any", "value": "[bar]", "namespace": null, "ignoreCase": null}]], "input[name*='foo[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "any", "value": "foo[bar]", "namespace": null, "ignoreCase": null}]], "input[name=\"action\"]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "action", "namespace": null, "ignoreCase": null}]], "input[name='action']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "action", "namespace": null, "ignoreCase": null}]], "input[name='foo[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "foo[bar]", "namespace": null, "ignoreCase": null}]], "input[name='types[]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "types[]", "namespace": null, "ignoreCase": null}]], "input[name=action]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "action", "namespace": null, "ignoreCase": null}]], "input[name=foo\\ bar]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "foo bar", "namespace": null, "ignoreCase": null}]], "input[name=foo\\.baz]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "foo.baz", "namespace": null, "ignoreCase": null}]], "input[name=foo\\[baz\\]]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "foo[baz]", "namespace": null, "ignoreCase": null}]], "input[name^='foo[']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "start", "value": "foo[", "namespace": null, "ignoreCase": null}]], "input[name^='foo[bar]']": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "name", "action": "start", "value": "foo[bar]", "namespace": null, "ignoreCase": null}]], "input[title=\"Don't click me\"]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "title", "action": "equals", "value": "Don't click me", "namespace": null, "ignoreCase": null}]], "input[value=Test]": [[{"type": "tag", "name": "input", "namespace": null}, {"type": "attribute", "name": "value", "action": "equals", "value": "Test", "namespace": null, "ignoreCase": null}]], "li": [[{"type": "tag", "name": "li", "namespace": null}]], "li ~ li": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "sibling"}, {"type": "tag", "name": "li", "namespace": null}]], "li#attr-child-boosh[attr=boosh]": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "li#item_1.first": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "item_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "first", "namespace": null, "ignoreCase": "quirks"}]], "li#item_1.first.nonexistent": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "item_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "first", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}]], "li#item_1.nonexistent": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "item_1", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "element", "value": "nonexistent", "namespace": null, "ignoreCase": "quirks"}]], "li#item_3[class]": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "item_3", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "class", "action": "exists", "value": "", "namespace": null, "ignoreCase": null}]], "li:contains(hello)": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "hello"}]], "li:contains(human)": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "human"}]], "li:contains(humans)": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "humans"}]], "li:not(:first-child)": [[{"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "pseudo", "name": "first-child", "data": null}]]}]], "meta property thing": [[{"type": "tag", "name": "meta", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "property", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "thing", "namespace": null}]], "nonexistent": [[{"type": "tag", "name": "nonexistent", "namespace": null}]], "ol > li[attr=\"boosh\"]:last-child": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "ol li": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "li", "namespace": null}]], "ol ol li#attr-child-boosh[attr=boosh]": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "ol", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "ol#list li#attr-child-boosh[attr=boosh]": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "descendant"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "ol#list>li#attr-child-boosh[attr=boosh]": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "list", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "attr-child-boosh", "namespace": null, "ignoreCase": "quirks"}, {"type": "attribute", "name": "attr", "action": "equals", "value": "boosh", "namespace": null, "ignoreCase": null}]], "ol:contains(human)": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "human"}]], "ol:contains(humans)": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "humans"}]], "ol:empty": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "pseudo", "name": "empty", "data": null}]], "ol>li": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}]], "ol>li+li": [[{"type": "tag", "name": "ol", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "li", "namespace": null}]], "option": [[{"type": "tag", "name": "option", "namespace": null}]], "option:first-child:contains('o')": [[{"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}, {"type": "pseudo", "name": "contains", "data": "o"}]], "p": [[{"type": "tag", "name": "p", "namespace": null}]], "p + p": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "adjacent"}, {"type": "tag", "name": "p", "namespace": null}]], "p .blog": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "descendant"}, {"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}]], "p < div": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "parent"}, {"type": "tag", "name": "div", "namespace": null}]], "p > * > *": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "universal", "namespace": null}, {"type": "child"}, {"type": "universal", "namespace": null}]], "p > a": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "p > a.blog": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "blog", "namespace": null, "ignoreCase": "quirks"}]], "p >a": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "p a[href*=#]": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "any", "value": "#", "namespace": null, "ignoreCase": null}]], "p a[href^=#]": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "a", "namespace": null}, {"type": "attribute", "name": "href", "action": "start", "value": "#", "namespace": null, "ignoreCase": null}]], "p#blargh": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "blargh", "namespace": null, "ignoreCase": "quirks"}]], "p#firstp + p": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "firstp", "namespace": null, "ignoreCase": "quirks"}, {"type": "adjacent"}, {"type": "tag", "name": "p", "namespace": null}]], "p#strong": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "strong", "namespace": null, "ignoreCase": "quirks"}]], "p, div p": [[{"type": "tag", "name": "p", "namespace": null}], [{"type": "tag", "name": "div", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "p", "namespace": null}]], "p,a": [[{"type": "tag", "name": "p", "namespace": null}], [{"type": "tag", "name": "a", "namespace": null}]], "p.first > a": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "first", "namespace": null, "ignoreCase": "quirks"}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "p.foo": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "foo", "namespace": null, "ignoreCase": "quirks"}]], "p.odd": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "odd", "namespace": null, "ignoreCase": "quirks"}]], "p:contains(bar)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "bar"}]], "p:contains(id=\"foo\")[id!=')']": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "id=\"foo\""}, {"type": "attribute", "name": "id", "action": "not", "value": ")", "namespace": null, "ignoreCase": null}]], "p:contains(id=\"foo\")[id!=\\)]": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "contains", "data": "id=\"foo\""}, {"type": "attribute", "name": "id", "action": "not", "value": ")", "namespace": null, "ignoreCase": null}]], "p:first-child": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "p:has( a )": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}]], "p:has(a)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "has", "data": [[{"type": "tag", "name": "a", "namespace": null}]]}]], "p:last-child": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "p:not(a,p)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "p", "namespace": null}]]}]], "p:not(a,p,b)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "a", "namespace": null}], [{"type": "tag", "name": "p", "namespace": null}], [{"type": "tag", "name": "b", "namespace": null}]]}]], "p:not(p)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "p", "namespace": null}]]}]], "p:not(p,a)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "not", "data": [[{"type": "tag", "name": "p", "namespace": null}], [{"type": "tag", "name": "a", "namespace": null}]]}]], "p:nth-child( 1 )": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": " 1 "}]], "p:nth-child(1)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "1"}]], "p:nth-child(2)": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2"}]], "p> a": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "p>a": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "a", "namespace": null}]], "p[lang=en] + p": [[{"type": "tag", "name": "p", "namespace": null}, {"type": "attribute", "name": "lang", "action": "equals", "value": "en", "namespace": null, "ignoreCase": null}, {"type": "adjacent"}, {"type": "tag", "name": "p", "namespace": null}]], "param": [[{"type": "tag", "name": "param", "namespace": null}]], "property[name=prop2]": [[{"type": "tag", "name": "property", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "prop2", "namespace": null, "ignoreCase": null}]], "select": [[{"type": "tag", "name": "select", "namespace": null}]], "select[name='select2'] option:selected": [[{"type": "tag", "name": "select", "namespace": null}, {"type": "attribute", "name": "name", "action": "equals", "value": "select2", "namespace": null, "ignoreCase": null}, {"type": "descendant"}, {"type": "tag", "name": "option", "namespace": null}, {"type": "pseudo", "name": "selected", "data": null}]], "soap\\:Envelope": [[{"type": "tag", "name": "soap:Envelope", "namespace": null}]], "span": [[{"type": "tag", "name": "span", "namespace": null}]], "span > span": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "span", "namespace": null}]], "span span": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "descendant"}, {"type": "tag", "name": "span", "namespace": null}]], "span ~ #level3_2": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "sibling"}, {"type": "attribute", "name": "id", "action": "equals", "value": "level3_2", "namespace": null, "ignoreCase": "quirks"}]], "span#dupL1": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "dupL1", "namespace": null, "ignoreCase": "quirks"}]], "span.span_bar": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "span_bar", "namespace": null, "ignoreCase": "quirks"}]], "span.span_foo": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "span_foo", "namespace": null, "ignoreCase": "quirks"}]], "span.span_wtf": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "attribute", "name": "class", "action": "element", "value": "span_wtf", "namespace": null, "ignoreCase": "quirks"}]], "span:empty > *": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "empty", "data": null}, {"type": "child"}, {"type": "universal", "namespace": null}]], "span:first-child": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "span:nth-child(5)": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "5"}]], "span[lang=中文]": [[{"type": "tag", "name": "span", "namespace": null}, {"type": "attribute", "name": "lang", "action": "equals", "value": "中文", "namespace": null, "ignoreCase": null}]], "strong": [[{"type": "tag", "name": "strong", "namespace": null}]], "strong#strong": [[{"type": "tag", "name": "strong", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "strong", "namespace": null, "ignoreCase": "quirks"}]], "tostring#toString": [[{"type": "tag", "name": "tostring", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "toString", "namespace": null, "ignoreCase": "quirks"}]], "ul > li": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}]], "ul > li:first-child": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "first-child", "data": null}]], "ul > li:last-child": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "last-child", "data": null}]], "ul > li:nth-child(1)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "1"}]], "ul > li:nth-child(2n)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2n"}]], "ul > li:nth-child(2n+1)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "2n+1"}]], "ul > li:nth-child(even)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "even"}]], "ul > li:nth-child(n)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "n"}]], "ul > li:nth-child(n-128)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "n-128"}]], "ul > li:nth-child(odd)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-child", "data": "odd"}]], "ul > li:nth-last-child(1)": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}, {"type": "pseudo", "name": "nth-last-child", "data": "1"}]], "ul#first": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "attribute", "name": "id", "action": "equals", "value": "first", "namespace": null, "ignoreCase": "quirks"}]], "ul:empty": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "pseudo", "name": "empty", "data": null}]], "ul>li": [[{"type": "tag", "name": "ul", "namespace": null}, {"type": "child"}, {"type": "tag", "name": "li", "namespace": null}]], ".before\\:h-\\[calc\\(100\\%-28px\\)\\]::before": [[{"type": "attribute", "name": "class", "action": "element", "value": "before:h-[calc(100%-28px)]", "namespace": null, "ignoreCase": "quirks"}, {"type": "pseudo-element", "name": "before", "data": null}]]}