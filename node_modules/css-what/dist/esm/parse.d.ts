import { type Selector, type Traversal } from "./types.js";
/**
 * Checks whether a specific selector is a traversal.
 * This is useful eg. in swapping the order of elements that
 * are not traversals.
 *
 * @param selector Selector to check.
 */
export declare function isTraversal(selector: Selector): selector is Traversal;
/**
 * Parses `selector`.
 *
 * @param selector Selector to parse.
 * @returns Returns a two-dimensional array.
 * The first dimension represents selectors separated by commas (eg. `sub1, sub2`),
 * the second contains the relevant tokens for that selector.
 */
export declare function parse(selector: string): Selector[][];
//# sourceMappingURL=parse.d.ts.map