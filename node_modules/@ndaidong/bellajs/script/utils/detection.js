"use strict";
// utils / detection
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasProperty = exports.isEmpty = exports.isEmail = exports.isDate = exports.isObject = exports.isFunction = exports.isNil = exports.isUndefined = exports.isNull = exports.isBoolean = exports.isString = exports.isArray = exports.isInteger = exports.isNumber = void 0;
const ob2Str = (val) => {
    return {}.toString.call(val);
};
const isNumber = (val) => {
    return Number(val) === val;
};
exports.isNumber = isNumber;
const isInteger = (val) => {
    return Number.isInteger(val);
};
exports.isInteger = isInteger;
const isArray = (val) => {
    return Array.isArray(val);
};
exports.isArray = isArray;
const isString = (val) => {
    return String(val) === val;
};
exports.isString = isString;
const isBoolean = (val) => {
    return Boolean(val) === val;
};
exports.isBoolean = isBoolean;
const isNull = (val) => {
    return ob2Str(val) === "[object Null]";
};
exports.isNull = isNull;
const isUndefined = (val) => {
    return ob2Str(val) === "[object Undefined]";
};
exports.isUndefined = isUndefined;
const isNil = (val) => {
    return (0, exports.isUndefined)(val) || (0, exports.isNull)(val);
};
exports.isNil = isNil;
const isFunction = (val) => {
    return ob2Str(val) === "[object Function]";
};
exports.isFunction = isFunction;
const isObject = (val) => {
    return ob2Str(val) === "[object Object]" && !(0, exports.isArray)(val);
};
exports.isObject = isObject;
const isDate = (val) => {
    return val instanceof Date && !isNaN(val.valueOf());
};
exports.isDate = isDate;
const isEmail = (val) => {
    const re = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i;
    return (0, exports.isString)(val) && re.test(val);
};
exports.isEmail = isEmail;
const isEmpty = (val) => {
    return !val || (0, exports.isNil)(val) ||
        ((0, exports.isString)(val) && val === "") ||
        ((0, exports.isArray)(val) && val.length === 0) ||
        ((0, exports.isObject)(val) && Object.keys(val).length === 0);
};
exports.isEmpty = isEmpty;
const hasProperty = (obj, prop) => {
    return Object.prototype.hasOwnProperty.call(obj, prop);
};
exports.hasProperty = hasProperty;
