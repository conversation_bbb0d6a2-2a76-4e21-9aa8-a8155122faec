"use strict";
// utils / curry
Object.defineProperty(exports, "__esModule", { value: true });
exports.curry = void 0;
const curry = (fn) => {
    const totalArguments = fn.length;
    const next = (argumentLength, rest) => {
        if (argumentLength > 0) {
            return (...args) => {
                return next(argumentLength - args.length, [...rest, ...args]);
            };
        }
        return fn(...rest);
    };
    return next(totalArguments, []);
};
exports.curry = curry;
