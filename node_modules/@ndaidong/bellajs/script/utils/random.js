"use strict";
// utils / random
Object.defineProperty(exports, "__esModule", { value: true });
exports.randint = exports.genid = void 0;
const crypto = globalThis.crypto;
const genid = (len = 32, prefix = "") => {
    let s = prefix;
    const nums = new Uint32Array(len);
    crypto.getRandomValues(nums);
    for (let i = 0; i < nums.length; i++) {
        const n = nums[i].toString(36);
        const r = Math.random();
        const c = n.charAt(Math.floor(r * n.length));
        s += r > 0.3 && r < 0.7 ? c.toUpperCase() : c;
    }
    return s.substring(0, len);
};
exports.genid = genid;
const randint = (min = 0, max = 1e6) => {
    const byteArray = new Uint32Array(1);
    crypto.getRandomValues(byteArray);
    const randomNumber = byteArray[0] / (0xffffffff + 1);
    return Math.floor(randomNumber * (max - min + 1)) + min;
};
exports.randint = randint;
