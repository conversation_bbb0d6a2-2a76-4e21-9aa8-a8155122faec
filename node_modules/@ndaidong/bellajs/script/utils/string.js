"use strict";
// utils / string
Object.defineProperty(exports, "__esModule", { value: true });
exports.slugify = exports.stripAccent = exports.replaceAll = exports.ucwords = exports.ucfirst = exports.unescapeHTML = exports.escapeHTML = exports.stripTags = exports.truncate = void 0;
const detection_js_1 = require("./detection.js");
const toString = (input) => {
    return !(0, detection_js_1.isString)(input) ? String(input) : input;
};
const truncate = (s, len = 140) => {
    const txt = toString(s);
    const txtlen = txt.length;
    if (txtlen <= len) {
        return txt;
    }
    const subtxt = txt.substring(0, len).trim();
    const subtxtArr = subtxt.split(" ");
    const subtxtLen = subtxtArr.length;
    if (subtxtLen > 1) {
        subtxtArr.pop();
        return subtxtArr.map((word) => word.trim()).join(" ") + "...";
    }
    return subtxt.substring(0, len - 3) + "...";
};
exports.truncate = truncate;
const stripTags = (s) => {
    return toString(s).replace(/(<([^>]+)>)/ig, "").trim();
};
exports.stripTags = stripTags;
const escapeHTML = (s) => {
    return toString(s)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;");
};
exports.escapeHTML = escapeHTML;
const unescapeHTML = (s) => {
    return toString(s)
        .replace(/&quot;/g, '"')
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&amp;/g, "&");
};
exports.unescapeHTML = unescapeHTML;
const ucfirst = (s) => {
    const x = toString(s).toLowerCase();
    return x.length > 1
        ? x.charAt(0).toUpperCase() + x.slice(1)
        : x.toUpperCase();
};
exports.ucfirst = ucfirst;
const ucwords = (s) => {
    return toString(s).split(" ").map((w) => {
        return (0, exports.ucfirst)(w);
    }).join(" ");
};
exports.ucwords = ucwords;
const replaceAll = (s, a, b) => {
    return toString(s).replaceAll(a, b);
};
exports.replaceAll = replaceAll;
const getCharMap = () => {
    const lmap = {
        a: "á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ|ä|æ",
        c: "ç",
        d: "đ|ð",
        e: "é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ|ë",
        i: "í|ì|ỉ|ĩ|ị|ï|î",
        n: "ñ",
        o: "ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ|ö|ø",
        s: "ß",
        u: "ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự|û",
        y: "ý|ỳ|ỷ|ỹ|ỵ|ÿ",
    };
    const map = {
        ...lmap,
    };
    Object.keys(lmap).forEach((k) => {
        const K = k.toUpperCase();
        map[K] = lmap[k].toUpperCase();
    });
    return map;
};
const stripAccent = (s) => {
    let x = toString(s);
    const updateS = (ai, key) => {
        x = (0, exports.replaceAll)(x, ai, key);
    };
    const map = getCharMap();
    for (const key in map) {
        if ((0, detection_js_1.hasProperty)(map, key)) {
            const a = map[key].split("|");
            a.forEach((item) => {
                return updateS(item, key);
            });
        }
    }
    return x;
};
exports.stripAccent = stripAccent;
const slugify = (s, delimiter = "-") => {
    return (0, exports.stripAccent)(s)
        .normalize("NFKD")
        .replace(/[\u0300-\u036f]/g, "")
        .trim()
        .toLowerCase()
        .replace(/[^a-z0-9 -]/g, "")
        .replace(/\s+/g, delimiter)
        .replace(new RegExp(`${delimiter}+`, "g"), delimiter);
};
exports.slugify = slugify;
