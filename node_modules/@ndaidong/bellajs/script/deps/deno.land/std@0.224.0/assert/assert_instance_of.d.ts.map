{"version": 3, "file": "assert_instance_of.d.ts", "sourceRoot": "", "sources": ["../../../../../src/deps/deno.land/std@0.224.0/assert/assert_instance_of.ts"], "names": [], "mappings": "AAIA,sBAAsB;AAEtB,MAAM,MAAM,cAAc,GAAG,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC;AACzD,4BAA4B;AAC5B,MAAM,MAAM,kBAAkB,CAAC,CAAC,SAAS,cAAc,IAAI,CAAC,SAC5D,KAAK,GAAG,IAAI,EAAE,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,GAC7B,KAAK,CAAC;AAEV;;;;;;;;;;;GAWG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,cAAc,EACvD,MAAM,EAAE,OAAO,EACf,YAAY,EAAE,CAAC,EACf,GAAG,SAAK,GACP,OAAO,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,CA6BzC"}