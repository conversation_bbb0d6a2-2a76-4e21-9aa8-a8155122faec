export type AnyObject = {
    [key: string]: any;
};
export declare const clone: (val: any, history?: any) => any;
export declare function copies(source: AnyObject, dest: AnyObject, matched?: boolean, excepts?: string[]): AnyObject;
export declare const unique: <T>(arr?: T[]) => T[];
export declare const sort: <T>(arr?: T[], sorting?: ((a: T, b: T) => number) | null) => T[];
export declare const sortBy: <T extends Record<string, any>>(arr?: T[], order?: number, key?: string) => T[];
export declare const shuffle: <T>(arr?: T[]) => T[];
export declare const pick: <T>(arr?: T[], count?: number) => T[];
export * from "./utils/detection.js";
export * from "./utils/string.js";
export * from "./utils/random.js";
export * from "./utils/date.js";
export * from "./utils/curry.js";
export * from "./utils/compose.js";
export * from "./utils/pipe.js";
//# sourceMappingURL=mod.d.ts.map