"use strict";
// mod.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pick = exports.shuffle = exports.sortBy = exports.sort = exports.unique = exports.clone = void 0;
exports.copies = copies;
const detection_js_1 = require("./utils/detection.js");
const clone = (val, history = null) => {
    const stack = history || new Set();
    if (stack.has(val)) {
        return val;
    }
    stack.add(val);
    if ((0, detection_js_1.isDate)(val)) {
        return new Date(val.valueOf());
    }
    const copyObject = (o) => {
        const oo = Object.create({});
        for (const k in o) {
            if ((0, detection_js_1.hasProperty)(o, k)) {
                oo[k] = (0, exports.clone)(o[k], stack);
            }
        }
        return oo;
    };
    const copyArray = (a) => {
        return [...a].map((e) => {
            if ((0, detection_js_1.isArray)(e)) {
                return copyArray(e);
            }
            else if ((0, detection_js_1.isObject)(e)) {
                return copyObject(e);
            }
            return (0, exports.clone)(e, stack);
        });
    };
    if ((0, detection_js_1.isArray)(val)) {
        return copyArray(val);
    }
    if ((0, detection_js_1.isObject)(val)) {
        return copyObject(val);
    }
    return val;
};
exports.clone = clone;
function copies(source, dest, matched = false, excepts = []) {
    for (const k in source) {
        if (excepts.length > 0 && excepts.includes(k)) {
            continue;
        }
        if (!matched || (matched && (0, detection_js_1.hasProperty)(dest, k))) {
            const oa = source[k];
            const ob = dest[k];
            if (((0, detection_js_1.isObject)(ob) && (0, detection_js_1.isObject)(oa)) || ((0, detection_js_1.isArray)(ob) && (0, detection_js_1.isArray)(oa))) {
                dest[k] = copies(oa, dest[k], matched, excepts);
            }
            else {
                dest[k] = (0, exports.clone)(oa);
            }
        }
    }
    return dest;
}
const unique = (arr = []) => {
    return [...new Set(arr)];
};
exports.unique = unique;
const fnSort = (a, b) => {
    return a > b ? 1 : (a < b ? -1 : 0);
};
const sort = (arr = [], sorting = null) => {
    const tmp = [...arr];
    const fn = sorting || fnSort;
    tmp.sort(fn);
    return tmp;
};
exports.sort = sort;
const sortBy = (arr = [], order = 1, key = "") => {
    if (!(0, detection_js_1.isString)(key) || !(0, detection_js_1.hasProperty)(arr[0], key)) {
        return arr;
    }
    return (0, exports.sort)(arr, (m, n) => {
        return m[key] > n[key] ? order : (m[key] < n[key] ? (-1 * order) : 0);
    });
};
exports.sortBy = sortBy;
const shuffle = (arr = []) => {
    const input = [...arr];
    const output = [];
    let inputLen = input.length;
    while (inputLen > 0) {
        const index = Math.floor(Math.random() * inputLen);
        output.push(input.splice(index, 1)[0]);
        inputLen--;
    }
    return output;
};
exports.shuffle = shuffle;
const pick = (arr = [], count = 1) => {
    const a = (0, exports.shuffle)(arr);
    const mc = Math.max(1, count);
    const c = Math.min(mc, a.length - 1);
    return a.splice(0, c);
};
exports.pick = pick;
__exportStar(require("./utils/detection.js"), exports);
__exportStar(require("./utils/string.js"), exports);
__exportStar(require("./utils/random.js"), exports);
__exportStar(require("./utils/date.js"), exports);
__exportStar(require("./utils/curry.js"), exports);
__exportStar(require("./utils/compose.js"), exports);
__exportStar(require("./utils/pipe.js"), exports);
