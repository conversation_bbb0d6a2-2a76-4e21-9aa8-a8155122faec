// utils / string
import { hasProperty, isString } from "./detection.js";
const toString = (input) => {
    return !isString(input) ? String(input) : input;
};
export const truncate = (s, len = 140) => {
    const txt = toString(s);
    const txtlen = txt.length;
    if (txtlen <= len) {
        return txt;
    }
    const subtxt = txt.substring(0, len).trim();
    const subtxtArr = subtxt.split(" ");
    const subtxtLen = subtxtArr.length;
    if (subtxtLen > 1) {
        subtxtArr.pop();
        return subtxtArr.map((word) => word.trim()).join(" ") + "...";
    }
    return subtxt.substring(0, len - 3) + "...";
};
export const stripTags = (s) => {
    return toString(s).replace(/(<([^>]+)>)/ig, "").trim();
};
export const escapeHTML = (s) => {
    return toString(s)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;");
};
export const unescapeHTML = (s) => {
    return toString(s)
        .replace(/&quot;/g, '"')
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&amp;/g, "&");
};
export const ucfirst = (s) => {
    const x = toString(s).toLowerCase();
    return x.length > 1
        ? x.charAt(0).toUpperCase() + x.slice(1)
        : x.toUpperCase();
};
export const ucwords = (s) => {
    return toString(s).split(" ").map((w) => {
        return ucfirst(w);
    }).join(" ");
};
export const replaceAll = (s, a, b) => {
    return toString(s).replaceAll(a, b);
};
const getCharMap = () => {
    const lmap = {
        a: "á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ|ä|æ",
        c: "ç",
        d: "đ|ð",
        e: "é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ|ë",
        i: "í|ì|ỉ|ĩ|ị|ï|î",
        n: "ñ",
        o: "ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ|ö|ø",
        s: "ß",
        u: "ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự|û",
        y: "ý|ỳ|ỷ|ỹ|ỵ|ÿ",
    };
    const map = {
        ...lmap,
    };
    Object.keys(lmap).forEach((k) => {
        const K = k.toUpperCase();
        map[K] = lmap[k].toUpperCase();
    });
    return map;
};
export const stripAccent = (s) => {
    let x = toString(s);
    const updateS = (ai, key) => {
        x = replaceAll(x, ai, key);
    };
    const map = getCharMap();
    for (const key in map) {
        if (hasProperty(map, key)) {
            const a = map[key].split("|");
            a.forEach((item) => {
                return updateS(item, key);
            });
        }
    }
    return x;
};
export const slugify = (s, delimiter = "-") => {
    return stripAccent(s)
        .normalize("NFKD")
        .replace(/[\u0300-\u036f]/g, "")
        .trim()
        .toLowerCase()
        .replace(/[^a-z0-9 -]/g, "")
        .replace(/\s+/g, delimiter)
        .replace(new RegExp(`${delimiter}+`, "g"), delimiter);
};
