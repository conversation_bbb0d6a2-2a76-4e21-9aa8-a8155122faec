export declare const isNumber: (val: any) => boolean;
export declare const isInteger: (val: any) => boolean;
export declare const isArray: (val: any) => boolean;
export declare const isString: (val: any) => boolean;
export declare const isBoolean: (val: any) => boolean;
export declare const isNull: (val: any) => boolean;
export declare const isUndefined: (val: any) => boolean;
export declare const isNil: (val: any) => boolean;
export declare const isFunction: (val: any) => boolean;
export declare const isObject: (val: any) => boolean;
export declare const isDate: (val: any) => boolean;
export declare const isEmail: (val: any) => boolean;
export declare const isEmpty: (val: any) => boolean;
export declare const hasProperty: (obj: any, prop: string) => boolean;
//# sourceMappingURL=detection.d.ts.map