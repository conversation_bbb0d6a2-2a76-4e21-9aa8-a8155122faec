{"name": "@ndaidong/bellajs", "version": "12.0.1", "description": "A useful helper for any javascript program", "author": "@ndaidong", "repository": {"type": "git", "url": "git+https://github.com/ndaidong/bellajs.git"}, "license": "MIT", "bugs": {"url": "https://github.com/ndaidong/bellajs/issues"}, "main": "./script/mod.js", "module": "./esm/mod.js", "exports": {".": {"import": "./esm/mod.js", "require": "./script/mod.js"}}, "scripts": {"test": "node test_runner.js"}, "devDependencies": {"@types/node": "^20.9.0", "picocolors": "^1.0.0", "@deno/shim-deno": "~0.18.0"}, "_generatedBy": "dnt@dev"}